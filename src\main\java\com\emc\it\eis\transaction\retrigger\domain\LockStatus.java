package com.emc.it.eis.transaction.retrigger.domain;

public class LockStatus {
	private String entityId;
	private String process;

	public LockStatus(String entityId, String process) {
		this.entityId = entityId;
		this.process = process;
	}

	/**
	 * @return the entityId
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @return the process
	 */
	public String getProcess() {
		return process;
	}

	/**
	 * @param entityId
	 *            the entityId to set
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * @param process
	 *            the process to set
	 */
	public void setProcess(String process) {
		this.process = process;
	}

}
