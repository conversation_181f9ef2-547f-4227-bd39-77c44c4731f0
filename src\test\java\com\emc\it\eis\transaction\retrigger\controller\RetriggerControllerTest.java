package com.emc.it.eis.transaction.retrigger.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import com.emc.it.eis.transaction.retrigger.domain.RetriggerRequest;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.gateway.RetriggerGateway;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

@ExtendWith(MockitoExtension.class)
class RetriggerControllerTest {

	@InjectMocks
	private RetriggerController controller;

	@Mock
	RetriggerService service;

	@Mock
	RetriggerGateway retriggerGateway;

	@Test
	void retriggerTransaction() throws JsonParseException, JsonMappingException, IOException {
		RetriggerPayload payload = new RetriggerPayload();
		when(service.buildRetriggerPayload(Mockito.any(RetriggerRequest.class))).thenReturn(payload);

		when(retriggerGateway.submitRetriggerRequest(Mockito.any())).thenReturn(null);
		assertEquals(HttpStatus.ACCEPTED, controller.retriggerTransaction(new RetriggerRequest()));
	}

	@Test
	void getRetriggerPayload() {
		@SuppressWarnings("unused")
		List<RetriggerPayload> list = new ArrayList<>();
		assertNotNull(controller.getRetriggerPayload(""));
	}
}
