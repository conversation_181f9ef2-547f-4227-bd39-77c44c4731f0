Name:             transaction-retrigger-service-7b4ddd589d-wg6ds
Namespace:        aicshd-cit-dev
Priority:         0
Service Account:  default
Node:             wrk-10-218-81-174/*************
Start Time:       Sat, 02 Aug 2025 18:27:45 +0530
Labels:           app=transaction-retrigger-service
                  kob-admin-ns=aicshd-cit-dev
                  pod-template-hash=7b4ddd589d
Annotations:      cni.projectcalico.org/containerID: 6e74bab0cda02020c75107a4fba0c7d2cfb0d82fecbc14ea8ee4fd720c571c7f
                  cni.projectcalico.org/podIP: *************/32
                  cni.projectcalico.org/podIPs: *************/32
Status:           Running
SeccompProfile:   RuntimeDefault
IP:               *************
IPs:
  IP:           *************
Controlled By:  ReplicaSet/transaction-retrigger-service-7b4ddd589d
Containers:
  transaction-retrigger-service:
    Container ID:   containerd://82e94fb660cdc7a9bc58b12ff7d40074cfc1c5cad3b3d1b8703198d8b3869ba1
    Image:          harbor.dell.com/ipaas/realtime/feature/transaction-retrigger-service:feature-5.3.1
    Image ID:       harbor.dell.com/ipaas/realtime/feature/transaction-retrigger-service@sha256:71fbecee48ad253475c42c25556f32dad754beb65dc77046228dd00b59ee0275
    Port:           80/TCP
    Host Port:      0/TCP
    State:          Running
      Started:      Sat, 02 Aug 2025 18:28:03 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     1
      memory:  1Gi
    Requests:
      cpu:     100m
      memory:  1Gi
    Environment:
      SPRING_PROFILES_ACTIVE:       cloud,boot,shrd,shrdkob
      configproperties_sheet_name:  transactionRetriggerService,shared-datasource-config,shared-aicshd-postgres-cicadm
      app_config_name:              transactionRetriggerService
      _JAVA_OPTIONS:                -XX:MaxDirectMemorySize=1024m -Xmx1G -XX:ReservedCodeCacheSize=100M -XX:MaxMetaspaceSize=256M -Xss1m -XX:+UseG1GC -XX:+UseStringDeduplication -Dspring.threads.virtual.enabled=true
      APP_PORT:                     8080
      configserver_uri:             https://configserveruser:<EMAIL>/
      POD_NAMESPACE:                aicshd-cit-dev
      NODE_NAME:                     (v1:spec.nodeName)
      POD_NAME:                     transaction-retrigger-service-7b4ddd589d-wg6ds (v1:metadata.name)
      KUBERNETES_NAMESPACE:         aicshd-cit-dev (v1:metadata.namespace)
      APP_HOST:                      (v1:status.podIP)
    Mounts:
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-vqs9z (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True
  Initialized                 True
  Ready                       True
  ContainersReady             True
  PodScheduled                True
Volumes:
  kube-api-access-vqs9z:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    ConfigMapOptional:       <nil>
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              <none>
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
Events:
  Type    Reason     Age    From               Message
  ----    ------     ----   ----               -------
  Normal  Scheduled  7m20s  default-scheduler  Successfully assigned aicshd-cit-dev/transaction-retrigger-service-7b4ddd589d-wg6ds to wrk-10-218-81-174
  Normal  Pulling    7m19s  kubelet            Pulling image "harbor.dell.com/ipaas/realtime/feature/transaction-retrigger-service:feature-5.3.1"
  Normal  Pulled     7m11s  kubelet            Successfully pulled image "harbor.dell.com/ipaas/realtime/feature/transaction-retrigger-service:feature-5.3.1" in 8.435s (8.435s including waiting). Image size: 1900953427 bytes.
  Normal  Created    7m11s  kubelet            Created container: transaction-retrigger-service
  Normal  Started    7m3s   kubelet            Started container transaction-retrigger-service