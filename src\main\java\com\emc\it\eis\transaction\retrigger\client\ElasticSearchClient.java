package com.emc.it.eis.transaction.retrigger.client;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload2;


@Component
public class ElasticSearchClient {	
	
	private static final String BASIC = "Basic ";
	
	private static final String AUTHORIZATION = "Authorization";	
	
	@Value("${elastic.service.username}")
	private String userName;
	@Value("${elastic.service.password}")
	private String password;
	@Value("${elastic.search.url}")
	private String URL;
	
	@Autowired
	@Qualifier("restElkTemplate")
	private RestTemplate restElkTemplate;
	
	@Bean("restElkTemplate")
	public RestTemplate restTemplate(RestTemplateBuilder builder) { 
	  return builder.build();
	}
	
	private static final Logger LOGGER = LoggerFactory.getLogger(ElasticSearchClient.class);
	
	public Map<String, Object> getPropertiesfromELK(String index, String doc, ElasticSearchPayload payload) {
		
		Map<String, Object> responeMap = new HashMap<>();
		
		try {
		String plainCreds = userName + ":" + password;

		byte[] plainCredsBytes = plainCreds.getBytes();
		byte[] base64CredsBytes = Base64.getEncoder().encode(plainCredsBytes);
		String base64Creds = new String(base64CredsBytes);
		HttpHeaders headers = new HttpHeaders();

		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(AUTHORIZATION, BASIC + base64Creds);
		
		@SuppressWarnings("rawtypes")
		HttpEntity entity = new HttpEntity(payload, headers);
		
		String url = URL+"/"+index+"/"+doc;

		ResponseEntity<Map<String, Object>> response = restElkTemplate.exchange(url, HttpMethod.POST, entity,
				new ParameterizedTypeReference<Map<String, Object>>() {
				});
		
		responeMap = response.getBody();
		} catch (Exception e) {
			e.printStackTrace();
		}
		LOGGER.info("getPropertiesfromELK responeMap: {}", responeMap);		
		LOGGER.debug("response received: ");
		return responeMap;
	}
	
	public Map<String, Object> getCount(String index, ElasticSearchPayload esPayload) {
		Map<String, Object> responeMap = new HashMap<>();
		
		try {
			String plainCreds = userName + ":" + password;

			byte[] plainCredsBytes = plainCreds.getBytes();
			byte[] base64CredsBytes = Base64.getEncoder().encode(plainCredsBytes);
			String base64Creds = new String(base64CredsBytes);
			HttpHeaders headers = new HttpHeaders();

			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.add(AUTHORIZATION, BASIC + base64Creds);
			
			@SuppressWarnings("rawtypes")
			HttpEntity entity = new HttpEntity(esPayload, headers);
			
			String url = URL+"/"+index+"/_count";

			ResponseEntity<Map<String, Object>> response = restElkTemplate.exchange(url, HttpMethod.POST, entity,
					new ParameterizedTypeReference<Map<String, Object>>() {
					});
			
			responeMap = response.getBody();			
			
			} catch (Exception e) {
				e.printStackTrace();
			}
		LOGGER.info("getCount responeMap: {}", responeMap);
			return responeMap;
	}
	
	public Map<String, Object> getPayloadFromES(String index, ElasticSearchPayload2 esPayload) {
		Map<String, Object> responeMap = new HashMap<>();
		
		try {
			String plainCreds = userName + ":" + password;

			byte[] plainCredsBytes = plainCreds.getBytes();
			byte[] base64CredsBytes = Base64.getEncoder().encode(plainCredsBytes);
			String base64Creds = new String(base64CredsBytes);
			HttpHeaders headers = new HttpHeaders();

			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.add(AUTHORIZATION, BASIC + base64Creds);
			
			@SuppressWarnings("rawtypes")
			HttpEntity entity = new HttpEntity(esPayload, headers);
			
			String url = URL+"/"+index+"/_search";

			ResponseEntity<Map<String, Object>> response = restElkTemplate.exchange(url, HttpMethod.POST, entity,
					new ParameterizedTypeReference<Map<String, Object>>() {
					});
			
			responeMap = response.getBody();
			} catch (Exception e) {
				e.printStackTrace();
			}
		LOGGER.info("getPayloadFromES responeMap: {}", responeMap);
			return responeMap;
	}
	
	
}
