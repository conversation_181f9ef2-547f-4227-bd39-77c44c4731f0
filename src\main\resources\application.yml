spring:
  application:
    name: ${app_config_name}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
---
spring:
  config.activate.on-profile: ${SPRING_PROFILES_ACTIVE}
  cloud:
    config:
      name: ${configproperties_sheet_name} 
  config.import: optional:configserver:${configserver_uri}    
management:
  endpoints:    
     web:
       exposure:
         include: '*'
         exclude: 'beans'
