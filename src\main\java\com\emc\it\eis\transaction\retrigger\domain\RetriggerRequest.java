package com.emc.it.eis.transaction.retrigger.domain;

import com.emc.it.eis.alert.service.api.domain.RetriggerParams;

public class RetriggerRequest {
	private String appName;
	private String transactionId;
	private RetriggerParams retriggerParams;

	/**
	 * @return the transactionId
	 */
	public String getTransactionId() {
		return transactionId;
	}

	/**
	 * @param transactionId
	 *            the transactionId to set
	 */
	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	/**
	 * @return the retriggerParams
	 */
	public RetriggerParams getRetriggerParams() {
		return retriggerParams;
	}

	/**
	 * @param retriggerParams
	 *            the retriggerParams to set
	 */
	public void setRetriggerParams(RetriggerParams retriggerParams) {
		this.retriggerParams = retriggerParams;
	}

	/**
	 * @return the appName
	 */
	public String getAppName() {
		return appName;
	}

	/**
	 * @param appName the appName to set
	 */
	public void setAppName(String appName) {
		this.appName = appName;
	}

}
