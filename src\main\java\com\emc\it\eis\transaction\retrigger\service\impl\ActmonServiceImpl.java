package com.emc.it.eis.transaction.retrigger.service.impl;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;
import java.util.UUID;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Service;

import com.emc.it.eis.alert.service.api.domain.GenericTemplate;
import com.emc.it.eis.common.log4j.Environment;
import com.emc.it.eis.transaction.retrigger.document.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.domain.ActmonProperties;
import com.emc.it.eis.transaction.retrigger.service.ActmonService;
import com.emc.it.eis.transaction.retrigger.util.RetriggerUtil;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity;
import com.emc.it.enterprise.data.v1.DateTimeType;
import com.emc.it.enterprise.data.v1.TextType;
import com.emc.it.enterprise.msg.v1.ApplicationProfileType;
import com.emc.it.enterprise.msg.v1.MessageProfileType;
import com.emc.it.enterprise.msg.v1.PayloadContext;
import com.emc.it.enterprise.msg.v1.StatusType;
import com.emc.it.enterprise.msg.v1.StepClassificationType;
import com.emc.it.enterprise.msg.v1.TransactionProfileType;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class ActmonServiceImpl implements ActmonService {
	@Autowired
	private ActmonProperties properties;
	
	@Autowired
	private ObjectMapper objectMapper;

	public ObjectMapper getObjectMapper() {
		return objectMapper;
	}

	public void setObjectMapper(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}


	private String host;

	public static final String GLOBAL_TRANSACTION_ID = "EMC.globalTransactionId";


	@Override
	public CreateEventRequest getActmonEvent(MessagingException message) {
		CreateEventRequest event = new CreateEventRequest();
		event.setPayloadContext(new PayloadContext());
		event.setDocument(new Document());
		// Set Transaction Profile
		event.getPayloadContext().setTransactionProfile(
				new TransactionProfileType());
		event.getPayloadContext().getTransactionProfile()
				.setEnvironment(properties.getEnvironemnt());
		event.getPayloadContext().getTransactionProfile()
				.setGlobalTransactionID(UUID.randomUUID().toString());
		DateTimeType now = new DateTimeType();
		now.setValue(new DateTime());
		event.getPayloadContext().getTransactionProfile()
				.setTransactionDateTime(now);

		// Set Message Profile
		event.getPayloadContext().setMessageProfile(new MessageProfileType());
		event.getPayloadContext().getMessageProfile()
				.setDomain(properties.getDomain());
		event.getPayloadContext().getMessageProfile()
				.setProcess(properties.getProcess());
		event.getPayloadContext().getMessageProfile()
				.setServiceName(properties.getServiceName());
		event.getPayloadContext().getMessageProfile()
				.setServiceVersion(properties.getServiceVerion());

		// Set App Profile
		event.getPayloadContext().setApplicationProfile(
				new ApplicationProfileType());
		event.getPayloadContext().getApplicationProfile()
				.setAppName(properties.getAppName());

		// Set Document
		event.getDocument().setEventActivity(new EventActivity());
		event.getDocument().getEventActivity().setEvent("PROCESS");
		event.getDocument().getEventActivity()
				.setStep(new StepClassificationType());
		event.getDocument().getEventActivity().getStep()
				.setValue("errorChannel");
		event.getDocument().getEventActivity().setStatus(new StatusType());
		event.getDocument().getEventActivity().getStatus().setValue("FAILED");
		event.getDocument().getEventActivity().setDetail(new TextType());
		event.getDocument().getEventActivity().getDetail()
				.setValue(message.getMessage());
		event.getDocument().getEventActivity()
				.setThreadID(Thread.currentThread().getName());
		event.getDocument().getEventActivity().setHostName(getHost());

		Object failedMsg = message.getFailedMessage().getPayload();
		if (failedMsg instanceof RetriggerPayload obj) {
			event.getDocument()
					.getEventActivity()
					.setBusinessIdentifier(
							obj.getDedupProcess() + "|"
									+ obj.getGlobalTransactionId());
		} else if (failedMsg instanceof GenericTemplate obj) {
			event.getDocument()
					.getEventActivity()
					.setBusinessIdentifier(
							obj.getRetriggerParams().getDedupProcess()
									+ "|"
									+ RetriggerUtil.getValue(obj.getEvent()
											.getDocument().getEventActivity()
											.getNameValuePairs()
											.getNameValuePairs(),
											"GLOBAL_TRANSACTION_ID"));
		}
		return event;
	}

	@SuppressWarnings("unchecked")
	private String getHost() {
		if (host != null) {
			return host;
		}

		if (Environment.getEnvironmentVariable("VCAP_APPLICATION")!= null) {
			ObjectMapper mapper = getObjectMapper();
			try {
				Map<String, Object> rawServices = (Map) mapper.readValue(
						Environment.getEnvironmentVariable("VCAP_APPLICATION"),
						Map.class);
				host = rawServices.get("space_name") + "_"
						+ rawServices.get("instance_index");
			} catch (Exception e) {
				e.printStackTrace();
				host = "unkonwn";
			}

		} else {
			try {
				host = InetAddress.getLocalHost().getHostName();
			} catch (UnknownHostException e) {
				e.printStackTrace();
				host = "unkonwn";
			}
		}
		return host;
	}
}
