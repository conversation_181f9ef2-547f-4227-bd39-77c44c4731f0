package com.emc.it.eis.transaction.retrigger.util;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.function.Predicate;

import com.emc.it.eis.transaction.retrigger.domain.Property;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;

public final class RetriggerUtil {

	private RetriggerUtil() {
		throw new IllegalStateException("Utility class");
	}

	public static String getValue(List<NameValuePair> nameValuePairs, String key) {
		Predicate<NameValuePair> predicate = value -> value.getName().equalsIgnoreCase(key);
		String nameValuePairValue = null;
		try {

			Optional<NameValuePair> nameValuePairFilterResult = nameValuePairs.stream().filter(predicate).findFirst();

			NameValuePair nameValuePair = null;
			if (nameValuePairFilterResult.isPresent()) {
				nameValuePair = nameValuePairFilterResult.get();
				nameValuePairValue = nameValuePair.getValue();
			}
			return nameValuePairValue;
		} catch (NoSuchElementException exception) {
			return null;
		}

	}

	public static String getValueFromProperties(List<Property> properties, String key) {
		Predicate<Property> predicate = value -> value.getName().equalsIgnoreCase(key);
		String propertyValue = null;
		try {
			Optional<Property> propertyFilterResult = properties.stream().filter(predicate).findFirst();
			Property property = null;
			if (propertyFilterResult.isPresent()) {
				property = propertyFilterResult.get();
				propertyValue = property.getValue();
			}
			return propertyValue;
		} catch (NoSuchElementException exception) {
			return null;
		}
	}

	public static String formatString(String regex, String replacement, String value) {
		return value.replaceAll(regex, replacement);
	}
}
