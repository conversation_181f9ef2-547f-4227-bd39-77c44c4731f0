package com.emc.it.eis.transaction.retrigger.domain;

import java.util.ArrayList;
import java.util.List;

public class Transaction {
	private String id;
	private List<String> appName;
	private List<String> status;
	private String gid;
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @return the appName
	 */
	public List<String> getAppName() {
		return appName;
	}

	/**
	 * @return the gid
	 */
	public String getGid() {
		return gid;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @param appName
	 *            the appName to set
	 */
	public void setAppName(String appName) {
		if (this.appName == null) {
			this.appName = new ArrayList<>();
			this.appName.add(appName);
		} else {
			this.appName.add(appName);
		}

	}

	/**
	 * @param gid
	 *            the gid to set
	 */
	public void setGid(String gid) {
		this.gid = gid;
	}

	/**
	 * @return the page
	 */
	public int getPage() {
		return 0;
	}

	/**
	 * @return the size
	 */
	public int getSize() {
		return 10;
	}

	/**
	 * @return the sortColumn
	 */
	public String getSortColumn() {
		return "id";
	}

	/**
	 * @return the order
	 */
	public String getOrder() {
		return "asc";
	}

	/**
	 * @return the status
	 */
	public List<String> getStatus() {
		return status;
	}

	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		if (this.status == null) {
			this.status = new ArrayList<>();
			this.status.add(status);
		} else {
			this.status.add(status);
		}
	}
	
}
