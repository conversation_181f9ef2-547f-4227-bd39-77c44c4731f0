package com.emc.it.eis.transaction.retrigger.domain;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.MediaType;

public class ResubmitRequest {
	private String routing_key;
	private String payload;
	private Map<String, Object> properties;

	/**
	 * @return the routing_key
	 */
	public String getRouting_key() {
		return routing_key;
	}

	/**
	 * @return the delivery_mode
	 */
	public String getDelivery_mode() {
		return "2";
	}

	/**
	 * @return the payload
	 */
	public String getPayload() {
		return payload;
	}

	/**
	 * @return the payload_encoding
	 */
	public String getPayload_encoding() {
		return "string";
	}

	/**
	 * @return the properties
	 */
	public Map<String, Object> getProperties() {
		return properties;
	}

	/**
	 * @param routingKey
	 *            the routing_key to set
	 */
	public void setRouting_key(String routingKey) {
		this.routing_key = routingKey;
	}

	/**
	 * @param payload
	 *            the payload to set
	 */
	public void setPayload(String payload) {
		this.payload = payload;
	}

	/**
	 * @param properties
	 *            the properties to set
	 */
	public void setProperties(String key, Object value) {
		if (this.properties == null) {
			this.properties = new HashMap<>();
			properties.put("delivery_mode", 2);
			properties.put("content_type", MediaType.TEXT_PLAIN_VALUE);
		}
		this.properties.put(key, value);
	}
}
