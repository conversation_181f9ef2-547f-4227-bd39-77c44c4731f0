package com.emc.it.eis.transaction.retrigger.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.core.annotation.Order;

@Configuration
@EnableWebSecurity
public class WebSecurityConfiguration {
	private static final String[] CSRF_IGNORE = {"/**"};

	@Bean
	@Order(2)
	public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
		http.securityMatcher("/retrigger/**", "/actuator/**", "/swagger-ui/**", "/v3/api-docs/**")
			.authorizeHttpRequests(
				matchers -> matchers.requestMatchers(AntPathRequestMatcher.antMatcher("/**")).permitAll())
			.csrf(csrf -> csrf.ignoringRequestMatchers(CSRF_IGNORE));
		return http.build();
	}
}
