package com.emc.it.eis.transaction.retrigger.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.emc.it.eis.transaction.retrigger.config.RedisFeignConfiguration;
import com.emc.it.eis.transaction.retrigger.constants.Constants;

@FeignClient(contextId = Constants.REDIS_SERVICE_NAME,name = Constants.REDIS_SERVICE_NAME, url = Constants.REDIS_SERVICE_URL, configuration = RedisFeignConfiguration.class)
public interface RedisClient {
	
	@GetMapping(Constants.REDIS_VALUE)
	public String getValueOfKey(@RequestParam String key);
}
