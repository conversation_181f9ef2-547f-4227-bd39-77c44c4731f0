package com.emc.it.eis.transaction.retrigger.constants;

public interface Constants
{
	public static String ACTMON_SERVICE_NAME="actmon-service";
	public static String ACTMON_SERVICE_URL="${actmon.service.url}";
	public static String ACTMON_VALUE="/actmon.ws";
	
	public static String DEDUP_SERVICE_NAME="dedup-service";
	public static String DEDUP_SERVICE_URL="${dedup.service.url}";
	public static String DEDUP_VALUE="/{process}/{dedupKey}/{entityId}";
	

	
	
	public static String RABBITMQ_SERVICE_NAME="rabbitmq-service";
	public static String RABBITMQ_SERVICE_URL="${rabbitmq.service.url}";
	public static String RABBITMQ_VALUE="/api/exchanges/{vhost}/{exchange}/publish";
	
	public static String REDIS_SERVICE_NAME="redis-client";
	public static String REDIS_SERVICE_URL="${redis.service.url}";
	public static String REDIS_VALUE="/api/generic/inventory/redis";
	
	public static String STRICTORDER_SERVICE_NAME="strictorder-service";
	public static String STRICTORDER_SERVICE_URL="${strictorder.service.url}";
	public static String STRICTORDER_VALUE="/lockStatus/unlock";
	
	
	public static String TRANSACTION_SERVICE_NAME= "payload-service";
	public static String TRANSACTION_SERVICE_URL="${payload.service.url}" ;
	public static String TRANSACTION_VALUE= "payload";
	public static String TRANSACTION_SEARCH_VALUE= "search";
	public static String TRANSACTION_PAYLOAD_VALUE= "/transaction/payload";
	public static String TRANSACTION_PROPERTIES_VALUE= "/transaction/properties";
	public static String TRANSACTION_COUNT_VALUE= "/transaction/count";
	
	public static String DEDUP_DIAS_VALUE= "${transaction-retrigger-service-dedup-dias-creds}";
	public static String ACTMON_DIAS_VALUE= "${transaction-retrigger-service-activity-monitor-dias-creds}";
	public static String REDIS_DIAS_VALUE= "${transaction-retrigger-service-redis-dias-creds}";
	public static String TRANSACTION_DIAS_VALUE= "${transaction-retrigger-service-payload-dias-creds}";
	public static String STRICTORDER_DIAS_VALUE= "${transaction-retrigger-service-strict-order-dias-creds}";
	
	
}
