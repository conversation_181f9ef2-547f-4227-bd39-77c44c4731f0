package com.emc.it.eis.transaction.retrigger.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;

import com.emc.it.eis.oauth2client.FeignRequestInterceptor;

import feign.Logger;
import feign.RequestInterceptor;

public class ActivityMonitoringFeignConfiguration {
	@Autowired
	OAuth2AuthorizedClientManager authorizedClientManager;
	@Bean
	public RequestInterceptor oauth2DedupFeignRequestInterceptor(ClientRegistrationRepository clientRegistrationRepository) {
		ClientRegistration clientRegistration = clientRegistrationRepository.findByRegistrationId("activity-monitoring-dias-creds");
		return new FeignRequestInterceptor(authorizedClientManager, clientRegistration);
	}

	@Bean
	Logger.Level feignLoggerLevel() {
		return Logger.Level.FULL;
	}
	
}
