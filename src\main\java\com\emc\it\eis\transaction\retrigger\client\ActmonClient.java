package com.emc.it.eis.transaction.retrigger.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.emc.it.eis.transaction.retrigger.config.ActivityMonitoringFeignConfiguration;
import com.emc.it.eis.transaction.retrigger.constants.Constants;
import com.emc.it.enterprise.data.v1.CreateEventRequest;

@FeignClient(contextId=Constants.ACTMON_SERVICE_NAME,name = Constants.ACTMON_SERVICE_NAME, url = Constants.ACTMON_SERVICE_URL,configuration= ActivityMonitoringFeignConfiguration.class)
public interface ActmonClient {
	@PostMapping(Constants.ACTMON_VALUE)
	String postActmonEvent(@RequestBody CreateEventRequest request);
}
