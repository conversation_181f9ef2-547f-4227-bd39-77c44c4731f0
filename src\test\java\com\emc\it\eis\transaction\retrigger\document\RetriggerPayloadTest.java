package com.emc.it.eis.transaction.retrigger.document;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Date;

import org.junit.jupiter.api.Test;

class RetriggerPayloadTest {

	@Test
	void data() {
		RetriggerPayload obj = new RetriggerPayload();

		obj.setDedupKey("test");
		assertEquals("test", obj.getDedupKey());	

		obj.setDedupProcess("test");
		assertNotNull(obj.getDedupProcess());

		obj.setDescription("test");
		assertNotNull(obj.getDescription());

		obj.setExchange("test");
		assertNotNull(obj.getExchange());

		obj.setGlobalTransactionId("test");
		assertNotNull(obj.getGlobalTransactionId());

		obj.setId("test");
		assertNotNull(obj.getId());

		obj.setRetriggerDate(new Date());
		assertNotNull(obj.getRetriggerDate());

		obj.setRetriggerPayload("test");
		assertNotNull(obj.getRetriggerPayload());

		obj.setRoutingKey("test");
		assertNotNull(obj.getRoutingKey());

		obj.setSource("test");
		assertNotNull(obj.getSource());

		obj.setStrictOrderKey("test");
		assertNotNull(obj.getStrictOrderKey());

		obj.setStrictOrderProcess("test");
		assertNotNull(obj.getStrictOrderProcess());

		obj.setvHost("test");
		assertNotNull(obj.getvHost());		
	}
}
