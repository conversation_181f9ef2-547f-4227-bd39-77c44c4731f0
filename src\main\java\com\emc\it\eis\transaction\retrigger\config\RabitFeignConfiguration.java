package com.emc.it.eis.transaction.retrigger.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import feign.Logger;
import feign.auth.BasicAuthRequestInterceptor;

public class RabitFeignConfiguration {
	
	@Bean
	public BasicAuthRequestInterceptor rabbitAuthRequestInterceptor(
			@Value("${rabbitmq.service.username}") String username,
			@Value("${rabbitmq.service.password}") String password) {
		return new BasicAuthRequestInterceptor(username, password);
	}

	@Bean
	Logger.Level feignLoggerLevel() {
		return Logger.Level.FULL;
	}
	
}
