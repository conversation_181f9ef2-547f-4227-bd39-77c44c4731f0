package com.emc.it.eis.transaction.retrigger.controller;

import java.io.IOException;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.emc.it.eis.transaction.retrigger.domain.RetriggerRequest;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.gateway.RetriggerGateway;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

@RestController
@RequestMapping("retrigger")
@Slf4j
public class RetriggerController {
	@Autowired
	private RetriggerService service;

	@Autowired
	private RetriggerGateway retriggerGateway;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(RetriggerController.class);

	@PostMapping(value = "transaction", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public HttpStatus retriggerTransaction(@RequestBody RetriggerRequest request) throws JsonParseException, JsonMappingException, IOException {
		LOGGER.info("Frontend Request:{}", request);
		RetriggerPayload payload = service.buildRetriggerPayload(request);
		
		LOGGER.info("RetriggerPayload Payload:{}", payload.toString());
		retriggerGateway.submitRetriggerRequest(payload);
		return HttpStatus.ACCEPTED;
	}

	@GetMapping("payload")
	public List<RetriggerPayload> getRetriggerPayload(
			@RequestParam String appName) {
		return service.getRetriggerPayload(appName);
	}
}
