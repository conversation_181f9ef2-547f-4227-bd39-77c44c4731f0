/**
 * 
 */
package com.emc.it.eis.transaction.retrigger;

import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;

import java.io.IOException;

import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.Header;

import com.emc.it.eis.transaction.retrigger.util.ResourceUtils;

/**
 * <AUTHOR>
 *
 */

public class MockHttpRequest {

	private ClientAndServer mockServer;

	public void startServer() throws IOException {
		mockServer = startClientAndServer(12345);
		mockHTTPRequests();
	}

	public void stopServer() {
		mockServer.stop();
	}

	public void mockHTTPRequests() throws IOException {		
		
		MockServerClient client = new MockServerClient("localhost", 12345);
		
		client.when(request().withMethod("GET").withPath("/retrigger/payload")).respond(response().withStatusCode(200)
				.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
		// .withBody(getRequestPayload("/data/agreement_list.json"))
		);

		client.when(request().withMethod("POST").withPath("/retrigger/transaction"))
				.respond(response().withStatusCode(200)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/retriggerRequest.json")));
		
		client.when(request().withMethod("GET").withPath("/audit/_count"))
				.respond(response().withStatusCode(200)
						.withBody("""
								{
								  "count": 81959785,
								  "_shards": {
								    "total": 5,
								    "successful": 5,
								    "skipped": 0,
								    "failed": 0
								  }
								}\
								"""));

		mockServer
				.when(request().withMethod("DELETE")
						.withPath("/dedup/Test_Process/eda32872-fe65-c0df-678b-50582d483d46/66681374"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody("{\"status\": \"FAILED\",\"message\": \"RECORD DOESNOT EXIST\"}"));

		client.when(request().withMethod("PUT").withPath("/api/lockStatus/unlock"))
				.respond(response().withStatusCode(202));

		client.when(request().withMethod("GET").withPath("/transaction/payload"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/payload.json")));
		client.when(request().withMethod("GET").withPath("/api/transaction/count"))
				.respond(response().withStatusCode(200)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/transactionCount.json")));

		client.when(request().withMethod("GET").withPath("/api/transaction/payload"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/payload_DB.json")));

		client.when(request().withMethod("POST").withPath("/transaction/search"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/transaction.json")));

		client.when(request().withMethod("POST").withPath("/api/exchaclnges/ACTMON/test.exg/publish"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody("{\"routed\": true}"));

		client.when(request().withMethod("POST").withPath("/actmon.ws")).respond(response().withStatusCode(202)
				.withHeaders(new Header("Content-Type", "application/json; charset=utf-8")).withBody("ok"));
		
		client.when(request().withMethod("GET").withPath("/api/transaction/properties"))
		.respond(response().withStatusCode(202)
				.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
				.withBody(getRequestPayload("/data/property.json")));
		
		/*client.when(request().withMethod("POST").withPath("/audit_dev/_search"))
		.respond(response().withStatusCode(202)
				.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
				.withBody(getRequestPayload("/data/elkProperties.json")));*/
		
		
		
		
		/*Map<String, Object> map = new HashMap<>();
		map.put("responseMap", getRequestPayload("/data/elkProperties.json"));
        Mockito.when(restTemplate.exchange(
            Matchers.eq("/audit_dev/_search"),
            Matchers.eq(HttpMethod.POST),
            Matchers.<HttpEntity>any(),
            Matchers.<ParameterizedTypeReference<Map<String, Object>>>any())
        ).thenReturn(new ResponseEntity<Map<String, Object>>(map,HttpStatus.ACCEPTED));*/

	}

	private String getRequestPayload(String file) throws IOException {
		return ResourceUtils.streamToString(getClass().getResourceAsStream(file));
	}

}
