package com.emc.it.eis.transaction.retrigger.domain;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class TransactionTest {

	@Test
	void data() {
		Transaction obj = new Transaction();

		obj.setAppName("test");
		assertNotNull(obj.getAppName());	

		obj.setGid("test");
		assertNotNull(obj.getGid());

		obj.setId("test");
		assertNotNull(obj.getId());

		obj.setStatus("test");
		assertNotNull(obj.getStatus());

		assertNotNull(obj.getOrder());
		assertTrue(obj.getPage() == 0);
		assertTrue(obj.getSize() == 10);
		assertNotNull(obj.getSortColumn());
		assertNotNull(obj.getStatus());			
	}
}
