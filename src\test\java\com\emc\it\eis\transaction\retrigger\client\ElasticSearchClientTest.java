package com.emc.it.eis.transaction.retrigger.client;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import java.util.HashMap;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload2;

@ExtendWith(MockitoExtension.class)
class ElasticSearchClientTest {

	@InjectMocks
	protected ElasticSearchClient elasticSearchClient;

	@Mock
	private RestTemplate restTemplate;

	@Test
	void getProperties() {

		ReflectionTestUtils.setField(elasticSearchClient, "userName", "test");
		ReflectionTestUtils.setField(elasticSearchClient, "password", "test");
		ReflectionTestUtils.setField(elasticSearchClient, "URL", "test");

		HashMap<String, Object> responseMap2 = new HashMap<>();
		responseMap2.put("totalPages", Integer.parseInt("90"));

		ResponseEntity<HashMap<String, Object>> response = new ResponseEntity<>(responseMap2, HttpStatus.OK);

		when(restTemplate.exchange(ArgumentMatchers.anyString(),
				ArgumentMatchers.any(HttpMethod.class),
				ArgumentMatchers.any(HttpEntity.class),
				ArgumentMatchers.any(ParameterizedTypeReference.class))).thenReturn(response); 

		assertNotNull(elasticSearchClient.getPropertiesfromELK("test", "test", new ElasticSearchPayload()));

		assertNotNull(elasticSearchClient.getCount("test", new ElasticSearchPayload()));

		assertNotNull(elasticSearchClient.getPayloadFromES("test", new ElasticSearchPayload2()));
	}
}
