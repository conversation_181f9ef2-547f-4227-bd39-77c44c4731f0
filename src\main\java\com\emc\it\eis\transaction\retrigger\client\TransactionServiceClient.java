package com.emc.it.eis.transaction.retrigger.client;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.emc.it.eis.transaction.retrigger.config.TransactionServiceFeignConfiguration;
import com.emc.it.eis.transaction.retrigger.constants.Constants;
import com.emc.it.eis.transaction.retrigger.domain.Payload;
import com.emc.it.eis.transaction.retrigger.domain.Property;
import com.emc.it.eis.transaction.retrigger.domain.Transaction;

@FeignClient(contextId =Constants.TRANSACTION_SERVICE_NAME  ,name = Constants.TRANSACTION_SERVICE_NAME , url = Constants.TRANSACTION_SERVICE_URL,configuration = TransactionServiceFeignConfiguration.class)
public interface TransactionServiceClient {
	@GetMapping(Constants.TRANSACTION_VALUE)
	Map<String, Object> getPayload(@RequestParam(value = "transaction.id") String transactionId);

	@PostMapping(Constants.TRANSACTION_SEARCH_VALUE)
	Map<String, Object> getTransaction(@RequestBody Transaction transaction);

	@GetMapping(Constants.TRANSACTION_PAYLOAD_VALUE)
	List<Payload> getPayloadfromDB(@RequestParam(value = "transaction.globalTransactionId") String globalTransactionId,
			@RequestParam(value = "transaction.status") String status,
			@RequestParam(value = "transaction.appName") String appName);

	@GetMapping("/transaction/properties")
	List<Property> getPropertiesfromDB(@RequestParam String transactionId);

	@GetMapping(Constants.TRANSACTION_COUNT_VALUE)
	Map<String, Integer> getTransactionCount(@RequestParam String businessIdentifier,
			@RequestParam String status, @RequestParam String appName,
			@RequestParam Date dateTime);
}
