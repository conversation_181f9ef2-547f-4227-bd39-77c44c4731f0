package com.emc.it.eis.transaction.retrigger.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.emc.it.eis.transaction.retrigger.config.StrictOrderFeignConfiguration;
import com.emc.it.eis.transaction.retrigger.constants.Constants;
import com.emc.it.eis.transaction.retrigger.domain.LockStatus;

@FeignClient(contextId = Constants.STRICTORDER_SERVICE_NAME, name = Constants.STRICTORDER_SERVICE_NAME, url = Constants.STRICTORDER_SERVICE_URL, configuration = StrictOrderFeignConfiguration.class)
public interface StrictOrderClient {
	@PutMapping(Constants.STRICTORDER_VALUE)
	String unlockRecord(@RequestBody LockStatus lockStatus);
}
