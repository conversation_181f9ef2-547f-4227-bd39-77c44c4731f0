package com.emc.it.eis.transaction.retrigger.domain;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
@Component
@ConfigurationProperties("transaction.retrigger")
public class AmqpProperties {
	private String amqpUrl;
	private String amqpUserName;
	private String amqpPassword;
	private String amqpVhost;
	private String retriggerParamQ;
	private String retriggerParamExchange;
	private String retriggerParamRoutingKey;
	private int paramQConsumers=10;
	private String lbExchange;
	private String lbRoutingKey;
	private String lbQueue;
	private int lbQConsumers=10;

	/**
	 * @return the amqpUrl
	 */
	public String getAmqpUrl() {
		return amqpUrl;
	}

	/**
	 * @param amqpUrl the amqpUrl to set
	 */
	public void setAmqpUrl(String amqpUrl) {
		this.amqpUrl = amqpUrl;
	}

	/**
	 * @return the amqpUserName
	 */
	public String getAmqpUserName() {
		return amqpUserName;
	}

	/**
	 * @param amqpUserName the amqpUserName to set
	 */
	public void setAmqpUserName(String amqpUserName) {
		this.amqpUserName = amqpUserName;
	}

	/**
	 * @return the amqpPassword
	 */
	public String getAmqpPassword() {
		return amqpPassword;
	}

	/**
	 * @param amqpPassword the amqpPassword to set
	 */
	public void setAmqpPassword(String amqpPassword) {
		this.amqpPassword = amqpPassword;
	}

	/**
	 * @return the amqpVhost
	 */
	public String getAmqpVhost() {
		return amqpVhost;
	}

	/**
	 * @param amqpVhost the amqpVhost to set
	 */
	public void setAmqpVhost(String amqpVhost) {
		this.amqpVhost = amqpVhost;
	}

	/**
	 * @return the retriggerParamQ
	 */
	public String getRetriggerParamQ() {
		return retriggerParamQ;
	}

	/**
	 * @param retriggerParamQ the retriggerParamQ to set
	 */
	public void setRetriggerParamQ(String retriggerParamQ) {
		this.retriggerParamQ = retriggerParamQ;
	}

	/**
	 * @return the paramQConsumers
	 */
	public int getParamQConsumers() {
		return paramQConsumers;
	}

	/**
	 * @param paramQConsumers the paramQConsumers to set
	 */
	public void setParamQConsumers(int paramQConsumers) {
		this.paramQConsumers = paramQConsumers;
	}

	/**
	 * @return the lbExchange
	 */
	public String getLbExchange() {
		return lbExchange;
	}

	/**
	 * @param lbExchange the lbExchange to set
	 */
	public void setLbExchange(String lbExchange) {
		this.lbExchange = lbExchange;
	}

	/**
	 * @return the lbRoutingKey
	 */
	public String getLbRoutingKey() {
		return lbRoutingKey;
	}

	/**
	 * @param lbRoutingKey the lbRoutingKey to set
	 */
	public void setLbRoutingKey(String lbRoutingKey) {
		this.lbRoutingKey = lbRoutingKey;
	}

	/**
	 * @return the lbQueue
	 */
	public String getLbQueue() {
		return lbQueue;
	}

	/**
	 * @param lbQueue the lbQueue to set
	 */
	public void setLbQueue(String lbQueue) {
		this.lbQueue = lbQueue;
	}

	/**
	 * @return the lbQConsumers
	 */
	public int getLbQConsumers() {
		return lbQConsumers;
	}

	/**
	 * @param lbQConsumers the lbQConsumers to set
	 */
	public void setLbQConsumers(int lbQConsumers) {
		this.lbQConsumers = lbQConsumers;
	}

	/**
	 * @return the retriggerParamExchange
	 */
	public String getRetriggerParamExchange() {
		return retriggerParamExchange;
	}

	/**
	 * @return the retriggerParamRoutingKey
	 */
	public String getRetriggerParamRoutingKey() {
		return retriggerParamRoutingKey;
	}

	/**
	 * @param retriggerParamExchange the retriggerParamExchange to set
	 */
	public void setRetriggerParamExchange(String retriggerParamExchange) {
		this.retriggerParamExchange = retriggerParamExchange;
	}

	/**
	 * @param retriggerParamRoutingKey the retriggerParamRoutingKey to set
	 */
	public void setRetriggerParamRoutingKey(String retriggerParamRoutingKey) {
		this.retriggerParamRoutingKey = retriggerParamRoutingKey;
	}
	
}
