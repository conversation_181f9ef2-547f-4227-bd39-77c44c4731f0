package com.emc.it.eis.transaction.retrigger.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

public class Term {

@JsonProperty("global_transaction_id")
@SerializedName("global_transaction_id")
private String globalTransactionId;

@JsonProperty("_id")
@SerializedName("_id")
private String id;

private String status;

@JsonProperty("app_name")
@SerializedName("app_name")
private String appName;

@JsonProperty("business_identifier")
@SerializedName("business_identifier")
private String businessIdentifier;

public String getGlobalTransactionId() {
	return globalTransactionId;
}

public void setGlobalTransactionId(String globalTransactionId) {
	this.globalTransactionId = globalTransactionId;
}

public String getId() {
	return id;
}

public void setId(String id) {
	this.id = id;
}

public String getStatus() {
return status;
}

public void setStatus(String status) {
this.status = status;
}

public String getAppName() {
return appName;
}

public void setAppName(String appName) {
this.appName = appName;
}

public String getBusinessIdentifier() {
	return businessIdentifier;
}

public void setBusinessIdentifier(String businessIdentifier) {
	this.businessIdentifier = businessIdentifier;
}

}
