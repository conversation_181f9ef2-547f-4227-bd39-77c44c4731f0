package com.emc.it.eis.transaction.retrigger.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.emc.it.eis.alert.service.api.domain.GenericTemplate;
import com.emc.it.eis.alert.service.api.domain.RetriggerParams;
import com.emc.it.eis.transaction.retrigger.client.ElasticSearchClient;
import com.emc.it.eis.transaction.retrigger.client.TransactionServiceClient;
import com.emc.it.eis.transaction.retrigger.domain.Bool;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload;
import com.emc.it.eis.transaction.retrigger.domain.Must;
import com.emc.it.eis.transaction.retrigger.domain.Property;
import com.emc.it.eis.transaction.retrigger.domain.Query;
import com.emc.it.eis.transaction.retrigger.domain.RetriggerRequest;
import com.emc.it.eis.transaction.retrigger.domain.Source;
import com.emc.it.eis.transaction.retrigger.domain.Term;
import com.emc.it.eis.transaction.retrigger.domain.Transaction;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;
import com.emc.it.eis.transaction.retrigger.util.RetriggerUtil;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class RetriggerServiceImpl implements RetriggerService {

	//@Autowired
	//private PayloadRepository repository;

	@Autowired
	private TransactionServiceClient payloadClient;
	
	@Autowired
	private ElasticSearchClient elasticSearchClient;
	
	@Autowired
	private ObjectMapper objectMapper;

	public ObjectMapper getObjectMapper() {
		return objectMapper;
	}

	public void setObjectMapper(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}

	@Value("${index}")
	String index;
	
	@Value("${esSearchParam}")
	String esSearchParam;
	
	@Value("${source.search.string}")
	String sourceSearchString;
	
	@Value("${es.key.details}")
	String esKeyDetails;
	
	public static final String GLOBAL_TRANSACTION_ID = "EMC.globalTransactionId";
	public static final String BUSINESS_IDENTIFIER = "EMC.businessIdentifier";
	public static final String DESCRIPTION = "Retriggred from Integration Flow";
	public static final String DESCRIPTION_1 = "Retriggred from Rest Api";
	public static final String UUID_REGEX = "(\\w{8})(\\w{4})(\\w{4})(\\w{4})(\\w{12})";
	
	private static final Logger LOGGER = LoggerFactory.getLogger(RetriggerServiceImpl.class);
	

	@Override
	public RetriggerPayload buildRetriggerPayload(GenericTemplate template) {
		RetriggerParams params = template.getRetriggerParams();
		List<NameValuePair> properties = template.getEvent().getDocument()
				.getEventActivity().getNameValuePairs().getNameValuePairs();
		RetriggerPayload payload = new RetriggerPayload();
		BeanUtils.copyProperties(params, payload);
		BeanUtils.copyProperties(params.getqDetail(), payload);

		payload.setId(UUID.randomUUID().toString());
		payload.setRetriggerApp(template.getEvent().getPayloadContext()
				.getApplicationProfile().getAppName());
		payload.setDedupKey(RetriggerUtil.getValue(properties,
				params.getDedupKey()));
		payload.setStrictOrderKey(RetriggerUtil.getValue(properties,
				params.getEntityKey()));
		payload.setGlobalTransactionId(RetriggerUtil.getValue(properties,
				GLOBAL_TRANSACTION_ID));
		payload.setBusinessIdentifer(RetriggerUtil.getValue(properties,
				BUSINESS_IDENTIFIER));
		payload.setRetriggerDate(new Date(System.currentTimeMillis()));
		payload.setTransactionDate(template.getEvent().getPayloadContext()
				.getTransactionProfile().getTransactionDateTime().getValue()
				.toDate());
		payload.setStatus("N");
		payload.setDescription(DESCRIPTION);
		LOGGER.info(" retriggerdate in build payload: {}", payload.getRetriggerDate());
		LOGGER.info(" getTransactionDate in build payload: {}", payload.getTransactionDate());
		return payload;
	}

	public RetriggerPayload saveRetriggerPayload(RetriggerPayload payload) {
		return null;
		//return repository.save(payload);
	}

	public RetriggerPayload updateRetriggerPayloadStatus(
			RetriggerPayload payload, String status) {
		payload.setStatus(status);
		return null;
		//return repository.save(payload);
	}

	@Override
	public List<RetriggerPayload> getRetriggerPayload() {
		return null;
		//return repository.findByRetriggerDateLessThanAndStatusEquals(
		//		new Date(), "N");
	}
	
	public ElasticSearchPayload constructPayload(String transactionId, String appName) throws JsonProcessingException {
		ElasticSearchPayload esPayload = new ElasticSearchPayload();
		Query query = new Query();
		Bool bool = new Bool();
		Must mustId = new Must();
		Must mustAppname = new Must();
		List<Must> mustList = new ArrayList<>();
		Term term = new Term();
		Term appTerm = new Term();
		term.setId(transactionId);
		term.setAppName(null);
		appTerm.setAppName(appName);
		appTerm.setId(null);
		mustId.setTerm(term);
		mustAppname.setTerm(appTerm);
		mustList.add(mustId);
		mustList.add(mustAppname);
		bool.setMust(mustList);
		query.setBool(bool);
		esPayload.setQuery(query);
		ObjectMapper object = getObjectMapper();
		Source source = new Source();
		List<String> excludesString = Arrays.asList(sourceSearchString.split(","));	
		source.setExcludes(excludesString);
		esPayload.setSource(source);
		String payload = object.writeValueAsString(esPayload);
		LOGGER.info("Source Payload{}", payload);
		return esPayload;
	}
	
	public String checkFlow(Map<String, Object> properties) {
		String flow = "RABBIT";
		Map<String, Object> hits = (Map<String, Object>) properties.get("hits");
		List<Map<String, Object>> hits1 = (List<Map<String, Object>>) hits.get("hits");
		if(!hits1.isEmpty())
		{
			Map<String, String> source = (Map<String, String>) hits1.get(0).get("_source");
			LOGGER.info("Partition Id:{}", source.get("partition_id"));
			if(source.get("partition_id")!=null) {
				flow = "KAFKA";
			}
		}
		return flow;
	}
	
	public List<Property> formatPayload(Map<String, Object> properties) throws JsonParseException, JsonMappingException, IOException {
		Map<String, Object> hits = (Map<String, Object>) properties.get("hits");
		List<Map<String, Object>> hits1 = (List<Map<String, Object>>) hits.get("hits");
		Map<String, String> source = (Map<String, String>) hits1.get(0).get("_source");
		LOGGER.info("Source{}", source);
		List<Property> props = new ArrayList<>();
		List<String> esKeys = Arrays.asList(esKeyDetails.split(","));
		LOGGER.info("ESKeys{}", esKeys);	
		Transaction transaction = new Transaction();
		transaction.setStatus(source.get("status"));
		transaction.setAppName(source.get("app_name"));
		transaction.setGid(source.get("global_transaction_id"));
		transaction.setId(hits1.get(0).get("_id").toString());

		int count = 0;
		List<LinkedHashMap> map = null;
		for (Map.Entry<String, String> entry : source.entrySet()) {
			count++;

				if((!esKeys.contains(entry.getKey())) && ("nvp".equals(entry.getKey()))) {
					ObjectMapper object = getObjectMapper();
					String value = entry.getValue();
					map = object.readValue(value, List.class);
					LOGGER.info("NVP:{}", map);
					
				}		
		}
		if (map != null) {
			for (LinkedHashMap entry : map) {
				Property property = new Property();
				property.setName(entry.get("name").toString());
				property.setValue(entry.get("value").toString());
				property.setTransaction(transaction);
				props.add(property);
			}
		}
		else
		{
			Property property = new Property();
			property.setName(GLOBAL_TRANSACTION_ID);
			property.setValue(transaction.getGid());
			property.setTransaction(transaction);
			props.add(property);
		}
		return props;
	}

	@Override
	public RetriggerPayload buildRetriggerPayload(RetriggerRequest request) throws JsonParseException, JsonMappingException, IOException {
		List<Property> properties = null;
		ElasticSearchPayload esPayload = constructPayload(request.getTransactionId(), request.getAppName());
		Map<String, Object> properties1 = elasticSearchClient.getPropertiesfromELK(index, esSearchParam, esPayload);
		if("KAFKA".equalsIgnoreCase(checkFlow(properties1))) {
			LOGGER.info("In kafka flow if loop");
			properties = formatPayload(properties1);
		}else {
			LOGGER.info("In Rabbit flow if loop");
			properties = payloadClient.getPropertiesfromDB(request
					.getTransactionId());
		}
		
		for( Property property : properties ) {
			LOGGER.debug("------------------------------------------------------------------");
			LOGGER.debug("Property Name : {}", property.getName());
			LOGGER.debug("Property Value: {}", property.getValue());
			LOGGER.debug("-------------------------------------------------------------------");
		}

		RetriggerPayload payload = new RetriggerPayload();
		BeanUtils.copyProperties(request.getRetriggerParams(), payload);
		BeanUtils.copyProperties(request.getRetriggerParams().getqDetail(),
				payload);
		String dedupKey = RetriggerUtil.getValueFromProperties(properties,
				request.getRetriggerParams().getDedupKey());
		LOGGER.info("DDK:{}", request.getRetriggerParams().getDedupKey());
		LOGGER.info("Dedup key:{}", dedupKey);
		LOGGER.info("Dedup process :{}", payload.getDedupProcess());
		LOGGER.info("Dedup process :{}", payload.getStrictOrderProcess());
		if (GLOBAL_TRANSACTION_ID
				.equalsIgnoreCase(request.getRetriggerParams().getDedupKey())
				&& !dedupKey.contains("-")) {

			dedupKey = RetriggerUtil.formatString(UUID_REGEX, "$1-$2-$3-$4-$5",
					dedupKey);
		}
		payload.setDedupKey(dedupKey);
		payload.setStrictOrderKey(RetriggerUtil.getValueFromProperties(
				properties, request.getRetriggerParams().getEntityKey()));
		LOGGER.info("StrictOrderKey key:{}", payload.getStrictOrderKey());
		String gid = RetriggerUtil.getValueFromProperties(properties,
				GLOBAL_TRANSACTION_ID);
		if (!gid.contains("-")) {
			gid = RetriggerUtil.formatString(UUID_REGEX, "$1-$2-$3-$4-$5", gid);
		}
		LOGGER.info("getRetriggerApp :{}", payload.getRetriggerApp());
		payload.setRetriggerApp(RetriggerUtil.getValueFromProperties(properties, "EMC.applicationId"));
		LOGGER.info("getRetriggerApp after set:{}", payload.getRetriggerApp());
		if( payload.getRetriggerApp() != null && ("ShipmentConsumerTms".equalsIgnoreCase(payload.getRetriggerApp()) 
				|| "ShipmentConsumerTradesphere".equalsIgnoreCase(payload.getRetriggerApp()) || "ShipmentConsumerGtm".equalsIgnoreCase(payload.getRetriggerApp()))) {
			LOGGER.info("Distribution component is either ShipmentConsumerTms or ShipmentConsumerTradesphere");
			if( !payload.getDedupKey().contains("C")) {
				LOGGER.info("Dedup key appending with C");
				payload.setDedupKey(payload.getDedupKey() + "C");
			}
			if( !payload.getStrictOrderKey().contains("C")) {
				LOGGER.info("StrictOrderKey key appending with C");
				payload.setStrictOrderKey(payload.getStrictOrderKey() + "C");
			}
			
			
		}
		payload.setId(UUID.randomUUID().toString());
	//	payload.setRetriggerApp(request.getAppName());
		payload.setGlobalTransactionId(gid);
		payload.setRetriggerDate(new Date(System.currentTimeMillis()
				+ request.getRetriggerParams().getInterval()));
		payload.setStatus("P");
		payload.setDescription(DESCRIPTION_1);
		return payload;
	}

	@Override
	public List<RetriggerPayload> getRetriggerPayload(String gid, String appName) {
		return null;
		//return repository.findByGlobalTransactionIdEqualsAndRetriggerAppEquals(
		//		gid, appName);
	}

	@Override
	public List<RetriggerPayload> getRetriggerPayload(String appName) {
		return null;
		//return repository.findByRetriggerAppEquals(appName);
	}

	@Override
	public List<RetriggerPayload> getRetriggerPayload(String bid,
			String appName, String status) {
		return null;
		//return repository
		//		.findByBusinessIdentiferEqualsAndRetriggerAppEqualsAndStatusEquals(
		//				bid, appName, status);
	}
	
	

}
