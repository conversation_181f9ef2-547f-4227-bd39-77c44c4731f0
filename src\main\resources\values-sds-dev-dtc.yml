# Default values for dell-vault-config-server.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicas: 1
appName: transaction-retrigger-service
# ingressWildcardDns: {{ingress_wildcard_dns}}
# clusterDomain: {{cluster_domain}}
namespace: aics360-cit-dev


image:
  repository: {{vaultconfigserver_image_repository}}
  tag: {{vaultconfigserver_image_tag}}
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.

imagePullSecrets: [docker-secret]
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  #create: false
  # Automatically mount a ServiceAccount's API credentials?
  #automount: false
  # Annotations to add to the service account
  #annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  #name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: 
  seccompProfile:
    type: RuntimeDefault
  allowPrivilegeEscalation: false
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000
  capabilities:
    drop:
      - ALL

certificate:
  issuerName: "clusterissuer"
  issuerKind: "ClusterIssuer"
  commonName: "transaction-retrigger-service-sds.cit-dev-a1-np.kob.dell.com"
  dnsName: "transaction-retrigger-service-sds.cit-dev-a1-np.kob.dell.com"
  #dnsName: "activity-monitoring-service-shd.aicshd-cit-dev-a1-np.kob.dell.com"
  #dnsName: "activity-monitoring-service-gsms.aics360-cit-dev-a1-np.kob.dell.com"
    
  issuergroup: "cert-issuer.digitalcloud.dell.com"




service:
  type: ClusterIP
  port: 80
  protocol: TCP
  targetPort: 8080
virtualserver:
  enabled: true
  host: "transaction-retrigger-service-sds.cit-dev-a1-np.kob.dell.com"
  #activity-monitoring-service-gsms.coredev-cnieap-r3-np.kob.dell.com


containerEnv:
  - name: SPRING_PROFILES_ACTIVE
    value: cloud,boot,sds,sdskob
  - name: configproperties_sheet_name
    value: transactionRetriggerService,shared-gemfire,shared-datasource-config,shared-aicshd-postgres-cicadm
  - name: APP_HOST
    value: CF_INSTANCE_IP
  - name: app_config_name
    value: transactionRetriggerService
  - name: _JAVA_OPTIONS
    value: -XX:MaxDirectMemorySize=1024m -Xmx1G -XX:ReservedCodeCacheSize=100M -XX:MaxMetaspaceSize=256M -Xss1m -XX:+UseG1GC -XX:+UseStringDeduplication -Dspring.threads.virtual.enabled=true
  - name: APP_PORT
    value: INSTANCE_PORT
  - name: configserver_uri
    value: https://configserveruser:<EMAIL>/


resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 1Gi




livenessProbe:
  httpGet:
    path: /
    port: http
readinessProbe:            
  httpGet:
    path: /actuator/health
    port: 8080
    initialDelaySeconds: 10
    periodSeconds: 5

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80


  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

#dockerconfigjson: "********************************************************************************************************************************************************************************************************************"

configmap:
  enabled: false
  app_name: vaultconfigserver_name 
  anotherKey: "another value"
  config_profiles: "composite,vault"
  gitlab_uri: "gitlab_uri"
  gitlab_project_access_token_name: "gitlab_project_access_token_name"
  gitlab_user: "gitlab_user"
  gitlab_password: "gitlab_password"
  config_server_user_name: "configserveruser"
  config_server_password: "configserveruser"
  gitlab_searchpaths: "gitlab_searchpaths"
  gitlab_label: "gitlab_label"
  vault_host: "vault_host"
  vault_port: "vault_port"
  vault_scheme: "vault_scheme"
  vault_namespace: "vault_namespace"
  vault_kv_version: "vault_kv_version"
  vault_secrets_engine: "vault_secrets_engine"
  vault_authentication: "vault_authentication"
  vault_role_id: "vault_role_id"
  vault_secret_id: "vault_secret_id"
