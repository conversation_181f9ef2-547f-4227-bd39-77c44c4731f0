<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.emc.it.eis</groupId>
    <artifactId>cic-rest-parent</artifactId>
    <version>6.2.0.RELEASE</version>
  </parent>
  <groupId>com.emc.it.eis</groupId>
  <artifactId>transaction-retrigger-service</artifactId>
  <version>${PROJECT_VERSION}</version>
  <packaging>war</packaging>
  <name>transaction-retrigger-service</name>
  <description>service for integration transaction retrigger</description>
  <dependencies>
    <dependency>
      <groupId>com.sun.xml.ws</groupId>
      <artifactId>jaxws-rt</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.emc.it.eis</groupId>
      <artifactId>alert-service-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.emc.it.eis</groupId>
      <artifactId>cic-integration-cluster</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mock-server</groupId>
      <artifactId>mockserver-netty</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>io.swagger.core.v3</groupId>
          <artifactId>swagger-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.emc.it.eis</groupId>
      <artifactId>oauth2-rest-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.emc.it.eis</groupId>
      <artifactId>oauth2-server</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>jacoco-initialize</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>jacoco-site</id>
            <phase>package</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <destFile>${basedir}/target/coverage-reports/jacoco-unit.exec</destFile>
          <dataFile>${basedir}/target/coverage-reports/jacoco-unit.exec</dataFile>
          <excludes>
            <exclude>com/emc/it/eis/*.class</exclude>
            <exclude>com/emc/it/eis/cic/contivo/transformers/v1/*.class</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
