package com.emc.it.eis.transaction.retrigger.integration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.Filter;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.MessageBuilder;

import com.emc.it.eis.transaction.retrigger.client.ActmonClient;
import com.emc.it.eis.transaction.retrigger.client.DedupServiceClient;
import com.emc.it.eis.transaction.retrigger.client.ElasticSearchClient;
import com.emc.it.eis.transaction.retrigger.client.RabbitMQClient;
import com.emc.it.eis.transaction.retrigger.client.RedisClient;
import com.emc.it.eis.transaction.retrigger.client.StrictOrderClient;
import com.emc.it.eis.transaction.retrigger.client.TransactionServiceClient;
import com.emc.it.eis.transaction.retrigger.domain.Bool;
import com.emc.it.eis.transaction.retrigger.domain.DateTime;
import com.emc.it.eis.transaction.retrigger.domain.ESFilter;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload2;
import com.emc.it.eis.transaction.retrigger.domain.LockStatus;
import com.emc.it.eis.transaction.retrigger.domain.Must;
import com.emc.it.eis.transaction.retrigger.domain.Payload;
import com.emc.it.eis.transaction.retrigger.domain.Query;
import com.emc.it.eis.transaction.retrigger.domain.Range;
import com.emc.it.eis.transaction.retrigger.domain.ResubmitRequest;
import com.emc.it.eis.transaction.retrigger.domain.Source1;
import com.emc.it.eis.transaction.retrigger.domain.Term;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.service.ActmonService;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Configuration
public class RetriggerConfig {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(RetriggerConfig.class);
	
	private static final String SOURCE_PAYLOAD = "Source Payload";
	
	private static final String STARTED= "STARTED";	
	
	@Autowired
	private RetriggerService service;

	@Autowired
	private DedupServiceClient dedupClient;

	@Autowired
	private StrictOrderClient strictOrderClient;

	@Autowired
	private TransactionServiceClient payloadClient;

	@Autowired
	private RabbitMQClient rabbitMQClient;

	@Autowired
	private ActmonClient actmonClient;

	@Autowired
	private ActmonService actmonService;

	@Autowired
	private RedisClient redisClient;
	
	@Autowired
	private ElasticSearchClient esClient;
	
	@Autowired
	private ObjectMapper objectMapper;

	public ObjectMapper getObjectMapper() {
		return objectMapper;
	}

	public void setObjectMapper(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}
	
	@Value("${index}")
	private String index;	
	
	
	@Value("${payloadParams}")
	private String payloadParams;
	
	@Bean
	public DirectChannel dedupDeleteIn() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel strictOrderUnlockIn() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel statusUpdateIn() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel selectPayloadIn() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel publishPayloadIn() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel amqpOutChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel errorIn() {
		return new DirectChannel();
	}
	
	@Bean
	public DirectChannel transactionfilterChannel() {
		return new DirectChannel();
	}
	
	public ElasticSearchPayload constructCountPayload(RetriggerPayload payload) {
		ElasticSearchPayload esPayload = new ElasticSearchPayload();
		Query query = new Query();
		Bool bool = new Bool();
		Must mustAppName = new Must();
		Must mustStatus = new Must();
		Must mustBid = new Must();
		List<Must> mustList = new ArrayList<>();
		Term termAppName = new Term();
		Term termStatus = new Term();
		Term termBid = new Term();
		ESFilter filter = new ESFilter();
		termAppName.setAppName(payload.getRetriggerApp());
		termStatus.setStatus(payload.getStatus());
		termStatus.setBusinessIdentifier(payload.getBusinessIdentifer());
		Range range = new Range();
		DateTime dateTime = new DateTime();
		dateTime.setGt(payload.getTransactionDate().toString());
		range.setDateTime(dateTime);
		mustAppName.setTerm(termAppName);
		mustList.add(mustAppName);
		mustStatus.setTerm(termStatus);
		mustList.add(mustStatus);
		mustBid.setTerm(termBid);
		mustList.add(mustBid);
		bool.setMust(mustList);
		List<ESFilter> filterList = new ArrayList<>();
		filterList.add(filter);
		bool.setFilter(filterList);
		query.setBool(bool);
		esPayload.setQuery(query);
		return esPayload;	
	}
	
//Get count using ES
	@Filter(inputChannel = "transactionfilterChannel", outputChannel = "dedupDeleteIn", discardChannel = "nullChannel")
	public boolean checkForDuplicateTransaction(RetriggerPayload payload) {
		if (payload.getBusinessIdentifer() == null) {
			return true;
		}
		/*
		 * LOGGER.info("Starting getting count"); ElasticSearchPayload esPayload =
		 * constructCountPayload(payload); Map<String, Object> esResponse =
		 * esClient.getCount(index, esPayload); Map<String, Integer> response = new
		 * HashMap<String, Integer>(); Integer count = (Integer)
		 * esResponse.get("_count"); LOGGER.info("Count:" + count);
		 * response.put("count", count);
		 * 
		 * if (response.get("count") == 0) { return true; } else {
		 * service.updateRetriggerPayloadStatus(payload, "D"); return false; }
		 */
		return true;
	}

	@ServiceActivator(inputChannel = "dedupDeleteIn", outputChannel = "strictOrderUnlockIn")
	public RetriggerPayload clearDedup(RetriggerPayload payload) throws JsonProcessingException {
		LOGGER.info("Inside clearDedup");
		ObjectMapper object = getObjectMapper();
		String payloadP = object.writeValueAsString(payload);
		LOGGER.info("{}{}", SOURCE_PAYLOAD, payloadP);
		if (payload.getDedupKey() == null
				|| payload.getStrictOrderKey() == null) {
			return payload;
		}
		
		LOGGER.info("Inside clearDedup");
		LOGGER.info("dedupProcess:{}", payload.getDedupProcess());
		LOGGER.info("dedupKey:{}", payload.getDedupKey());
		LOGGER.info("strictOrderKey:{}", payload.getStrictOrderKey());
		try {
		dedupClient.clearDedup(payload.getDedupProcess(),
				payload.getDedupKey(), payload.getStrictOrderKey());
		}catch(Exception e) {
			e.printStackTrace();
			LOGGER.info("Stacktrace:{}", e.getCause());
		}
		return payload;
	}

	@ServiceActivator(inputChannel = "strictOrderUnlockIn", outputChannel = "selectPayloadIn")
	public RetriggerPayload unlockStrictOrder(RetriggerPayload payload) throws JsonProcessingException {
		LOGGER.info("Inside unlockStrictOrder");
		LOGGER.info("Inside unlockStrictOrder{}", payload.getStrictOrderProcess());
		ObjectMapper object = getObjectMapper();
		String payloadP = object.writeValueAsString(payload);
		LOGGER.info("{}{}", SOURCE_PAYLOAD, payloadP);
		if (payload.getStrictOrderKey() == null
				|| "NA".equalsIgnoreCase(payload.getStrictOrderProcess())) {
			return payload;
		}
		
		LockStatus lockStatus = new LockStatus(payload.getStrictOrderKey(),
				payload.getStrictOrderProcess());
		try {
			strictOrderClient.unlockRecord(lockStatus);
		}catch(Exception e) {
			e.printStackTrace();
			LOGGER.info("Stacktrace strictorder:{}", e.getCause());
		}
		return payload;
	}
	
	public ElasticSearchPayload2 buildESQueryPayload(RetriggerPayload payload) throws JsonProcessingException {
		ObjectMapper object = getObjectMapper();
		String payloadP = object.writeValueAsString(payload);
		LOGGER.info("{}{}", SOURCE_PAYLOAD, payloadP);
		ElasticSearchPayload2 esPayload = new ElasticSearchPayload2();
		Query query = new Query();
		Bool bool = new Bool();
		Must shouldAppName = new Must();
		Must shouldGuid = new Must();
		Must shouldStatus = new Must();
		List<Must> shouldList = new ArrayList<>();
		Term term = new Term();
		term.setAppName(payload.getSource());
		LOGGER.info("appName:{}", payload.getSource());
		shouldAppName.setTerm(term);
		shouldList.add(shouldAppName);
		Term termGuid = new Term();
		LOGGER.info("GID:{}", payload.getGlobalTransactionId());
		termGuid.setGlobalTransactionId(payload.getGlobalTransactionId());
		shouldGuid.setTerm(termGuid);
		shouldList.add(shouldGuid);
		Term termStatus = new Term();
		termStatus.setStatus(STARTED);
		shouldStatus.setTerm(termStatus);
		shouldList.add(shouldStatus);
		bool.setMust(shouldList);
		query.setBool(bool);
		esPayload.setQuery(query);
		Source1 source = new Source1();
		List<String> param = Arrays.asList(payloadParams.split(","));	
		source.setIncludes(param);
		esPayload.setSource(source);
		String payloadObject = object.writeValueAsString(esPayload);
		LOGGER.info("Source Payload Gateway{}", payloadObject);
		return esPayload;
	}
		
	//Get payload method
	@ServiceActivator(inputChannel = "selectPayloadIn", outputChannel = "publishPayloadIn")
	public RetriggerPayload getPayloadfromDB(RetriggerPayload payload) throws JsonProcessingException {
		LOGGER.info("Inside selectPayloadIn");
		
		List<Payload> payloads = new ArrayList<>();
		ElasticSearchPayload2 esPayload = buildESQueryPayload(payload);
		Map<String, Object> payloadMap = esClient.getPayloadFromES(index, esPayload);
		Map<String, Object> hits = (Map<String, Object>) payloadMap.get("hits");
		List<Map<String, Object>> hits1 = (List<Map<String, Object>>) hits.get("hits");
		LOGGER.info("payloadMap-{}", payloadMap);
		LOGGER.info("Size of hits1:{}", hits1.size());
		if(!hits1.isEmpty()) {
			Map<String, String> source = (Map<String, String>) hits1.get(0).get("_source");
			if(source.get("partition_id")!=null) {
			LOGGER.info("Source:{}", source);
			LOGGER.info("Flow is kafka");
			Payload payloadNew = new Payload();
			String payloadDetails = source.get("payload");
			payloadNew.setPayload(payloadDetails);
			payloads.add(payloadNew);
			}
		else {
			LOGGER.info("Flow is rabbit, fetching payload from DB");
			payloads = payloadClient.getPayloadfromDB(
					payload.getGlobalTransactionId(), STARTED,
					payload.getSource());
			LOGGER.info("Fetched payload{}", payloads.get(0).getPayload());
		}
	  }
		else {
			LOGGER.info("Flow is rabbit, fetching payload from DB");
			payloads = payloadClient.getPayloadfromDB(
					payload.getGlobalTransactionId(), STARTED,
					payload.getSource());
			LOGGER.info("Fetched payload{}", payloads.get(0).getPayload());
		}
		
		if (!payloads.isEmpty()) {
			LOGGER.info("Fetched payload1{}", payloads.get(0).getPayload());
			payload.setRetriggerPayload(payloads.get(0).getPayload());
		} else {
			LOGGER.info("Payload not fetched");
			throw new MessagingException(MessageBuilder.withPayload(payload)
					.build(), "unable to get payload for gid: "
					+ payload.getGlobalTransactionId()
					+ " status: STARTED and source: " + payload.getSource());
		}
		return payload;
	}

	@ServiceActivator(inputChannel = "publishPayloadIn", outputChannel = "statusUpdateIn")
	public RetriggerPayload publishPayload(RetriggerPayload payload) {
		LOGGER.info("Inside publishPayloadIn");
		ResubmitRequest request = new ResubmitRequest();
		request.setPayload(payload.getRetriggerPayload());
		request.setRouting_key(payload.getRoutingKey());
		Properties headers = new Properties();
		headers.put("EMC.globalTransactionId", payload.getGlobalTransactionId());
		request.setProperties("headers", headers);
		rabbitMQClient.publishPayload(payload.getvHost(),
				payload.getExchange(), request);
		
		return payload;
	}

	@ServiceActivator(inputChannel = "statusUpdateIn", outputChannel = "loggingInChannel")
	public RetriggerPayload updateRetriggerPayload(RetriggerPayload payload) {
		LOGGER.info("Inside updateRetriggerPayload");
		return payload;
		//return service.updateRetriggerPayloadStatus(payload, "S");
	}

	@ServiceActivator(inputChannel = "errorChannel")
	public void handleError(MessagingException ex) {
		if (ex.getFailedMessage().getPayload() instanceof RetriggerPayload) {
			RetriggerPayload payload = (RetriggerPayload) ex.getFailedMessage()
					.getPayload();
			if (payload.getId() != null) {
				//service.updateRetriggerPayloadStatus(payload, "E");
			}
		}
		CreateEventRequest event = actmonService.getActmonEvent(ex);
		actmonClient.postActmonEvent(event);
	}
}
