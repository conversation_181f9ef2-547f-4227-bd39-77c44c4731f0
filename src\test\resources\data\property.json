[{"id": 3289247837, "name": "EMC.businessIdentifier", "value": "***********", "transaction": {"id": 443861148, "appName": "A02_Sub_Maestro_to_Click_BreakFix", "globalTransactionId": "execution-bfc460ee-620f-4460-82a4-dad...", "status": "INFO", "businessIdentifier": "***********", "dateTime": "2020-04-21T03:06:49.000+0000", "channelName": null}}, {"id": 3289247838, "name": "id", "value": "executionbfc460ee620f446082a4dadb634874de20200420", "transaction": {"id": 443861148, "appName": "A02_Sub_Maestro_to_Click_BreakFix", "globalTransactionId": "execution-bfc460ee-620f-4460-82a4-dad...", "status": "INFO", "businessIdentifier": "***********", "dateTime": "2020-04-21T03:06:49.000+0000", "channelName": null}}, {"id": 3289247839, "name": "EMC.applicationId", "value": "A02_Sub_Maestro_to_Click_BreakFix", "transaction": {"id": 443861148, "appName": "A02_Sub_Maestro_to_Click_BreakFix", "globalTransactionId": "execution-bfc460ee-620f-4460-82a4-dad...", "status": "INFO", "businessIdentifier": "***********", "dateTime": "2020-04-21T03:06:49.000+0000", "channelName": null}}, {"id": 3289247840, "name": "EMC.globalTransactionId", "value": "executionbfc460ee620f446082a4dadb634874de20200420", "transaction": {"id": 443861148, "appName": "A02_Sub_Maestro_to_Click_BreakFix", "globalTransactionId": "execution-bfc460ee-620f-4460-82a4-dad...", "status": "INFO", "businessIdentifier": "***********", "dateTime": "2020-04-21T03:06:49.000+0000", "channelName": null}}, {"id": 3289247841, "name": "PayloadContext.MessageProfile.Process", "value": "UNDEFINED", "transaction": {"id": 443861148, "appName": "A02_Sub_Maestro_to_Click_BreakFix", "globalTransactionId": "execution-bfc460ee-620f-4460-82a4-dad...", "status": "INFO", "businessIdentifier": "***********", "dateTime": "2020-04-21T03:06:49.000+0000", "channelName": null}}, {"id": 3289247842, "name": "timestamp", "value": "1587438409170", "transaction": {"id": 443861148, "appName": "A02_Sub_Maestro_to_Click_BreakFix", "globalTransactionId": "execution-bfc460ee-620f-4460-82a4-dad...", "status": "INFO", "businessIdentifier": "***********", "dateTime": "2020-04-21T03:06:49.000+0000", "channelName": null}}]