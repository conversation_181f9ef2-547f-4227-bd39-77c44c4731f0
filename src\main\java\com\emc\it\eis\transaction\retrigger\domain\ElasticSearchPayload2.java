package com.emc.it.eis.transaction.retrigger.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

public class ElasticSearchPayload2 {

private Query query;

@JsonProperty("_source")
@SerializedName("_source")
private Source1 source;

public Query getQuery() {
return query;
}

public void setQuery(Query query) {
this.query = query;
}

public Source1 getSource() {
return source;
}

public void setSource(Source1 source) {
this.source = source;
}


}
