package com.emc.it.eis.transaction.retrigger.document;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
@JsonIgnoreProperties(ignoreUnknown = true)
//@Document(indexName = "retrigger", type = "payload")
public class RetriggerPayload {
	@Id
	private String id;
	private String dedupKey;
	private String strictOrderKey;
	private String source;
	private String dedupProcess;
	private String strictOrderProcess;
	private String status;
	private Date retriggerDate;
	private String vHost;
	private String exchange;
	private String routingKey;
	private String globalTransactionId;
	private String description;
	@Transient
	private String transactionRetriggerPayload;
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the dedupKey
	 */
	public String getDedupKey() {
		return dedupKey;
	}

	/**
	 * @return the strictOrderKey
	 */
	public String getStrictOrderKey() {
		return strictOrderKey;
	}

	/**
	 * @return the vHost
	 */
	public String getvHost() {
		return vHost;
	}

	/**
	 * @return the exchange
	 */
	public String getExchange() {
		return exchange;
	}

	/**
	 * @return the routingKey
	 */
	public String getRoutingKey() {
		return routingKey;
	}

	/**
	 * @param dedupKey
	 *            the dedupKey to set
	 */
	public void setDedupKey(String dedupKey) {
		this.dedupKey = dedupKey;
	}

	/**
	 * @param strictOrderKey
	 *            the strictOrderKey to set
	 */
	public void setStrictOrderKey(String strictOrderKey) {
		this.strictOrderKey = strictOrderKey;
	}

	/**
	 * @param vHost
	 *            the vHost to set
	 */
	public void setvHost(String vHost) {
		this.vHost = vHost;
	}

	/**
	 * @param exchange
	 *            the exchange to set
	 */
	public void setExchange(String exchange) {
		this.exchange = exchange;
	}

	/**
	 * @param routingKey
	 *            the routingKey to set
	 */
	public void setRoutingKey(String routingKey) {
		this.routingKey = routingKey;
	}


	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}

	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * @return the retriggerDate
	 */
	public Date getRetriggerDate() {
		return retriggerDate;
	}

	/**
	 * @param retriggerDate
	 *            the retriggerDate to set
	 */
	public void setRetriggerDate(Date retriggerDate) {
		this.retriggerDate = retriggerDate;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the dedupProcess
	 */
	public String getDedupProcess() {
		return dedupProcess;
	}

	/**
	 * @param dedupProcess the dedupProcess to set
	 */
	public void setDedupProcess(String dedupProcess) {
		this.dedupProcess = dedupProcess;
	}

	/**
	 * @return the strictOrderProcess
	 */
	public String getStrictOrderProcess() {
		return strictOrderProcess;
	}

	/**
	 * @param strictOrderProcess the strictOrderProcess to set
	 */
	public void setStrictOrderProcess(String strictOrderProcess) {
		this.strictOrderProcess = strictOrderProcess;
	}

	/**
	 * @return the globalTransactionId
	 */
	public String getGlobalTransactionId() {
		return globalTransactionId;
	}

	/**
	 * @param globalTransactionId the globalTransactionId to set
	 */
	public void setGlobalTransactionId(String globalTransactionId) {
		this.globalTransactionId = globalTransactionId;
	}

	/**
	 * @return the retriggerPayload
	 */
	public String getRetriggerPayload() {
		return transactionRetriggerPayload;
	}

	/**
	 * @param retriggerPayload the retriggerPayload to set
	 */
	public void setRetriggerPayload(String retriggerPayload) {
		this.transactionRetriggerPayload = retriggerPayload;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}			
}
