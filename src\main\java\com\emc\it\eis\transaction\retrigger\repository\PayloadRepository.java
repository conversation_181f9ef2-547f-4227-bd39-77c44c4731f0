package com.emc.it.eis.transaction.retrigger.repository;

public interface PayloadRepository{ //extends GemfireRepository<RetriggerPayload, String>{
	//ElasticsearchRepository
	/*
	 * public List<RetriggerPayload> findByRetriggerDateLessThanAndStatusEquals(Date
	 * date, String status);
	 * 
	 * public List<RetriggerPayload>
	 * findByGlobalTransactionIdEqualsAndRetriggerAppEquals(String
	 * globalTransactionId, String retriggerApp);
	 * 
	 * public List<RetriggerPayload>
	 * findByBusinessIdentiferEqualsAndRetriggerAppEqualsAndStatusEquals(String
	 * businessIdentifer, String retriggerApp, String status);
	 * 
	 * public List<RetriggerPayload> findByRetriggerAppEquals(String retriggerApp);
	 */
}
