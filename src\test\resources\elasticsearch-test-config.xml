<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:elasticsearch="http://www.springframework.org/schema/data/elasticsearch"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/data/elasticsearch http://www.springframework.org/schema/data/elasticsearch/spring-elasticsearch.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<!-- <elasticsearch:node-client id="client" local="true" /> -->

	<!--<elasticsearch:transport-client id="client" cluster-name="elasticsearch" 
		cluster-nodes="127.0.0.1:9300"/> -->
	<!-- <bean name="elasticsearchTemplate" class="org.springframework.data.elasticsearch.core.ElasticsearchTemplate"> 
		<constructor-arg name="client" ref="client" /> </bean> <elasticsearch:repositories 
		base-package="org.springframework.data.elasticsearch.repositories" /> -->

	<util:properties id="gemFireProperties">
		<prop key="log-level">info</prop>
		<prop key="mcast-port">0</prop>
	</util:properties>

	<!--<gfe:replicated-region id="retriggerCache" />
	
	<gfe:cache properties-ref="gemFireProperties"
		use-bean-factory-locator="false" />-->

</beans>
