package com.emc.it.eis.transaction.retrigger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.Header;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandlingException;
import org.springframework.messaging.PollableChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.alert.service.api.domain.GenericTemplate;
import com.emc.it.eis.transaction.retrigger.client.ElasticSearchClient;
import com.emc.it.eis.transaction.retrigger.client.RabbitMQClient;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;
import com.emc.it.eis.transaction.retrigger.util.ResourceUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.joda.JodaModule;

@SpringBootTest(classes = {TransactionRetriggerServiceApplication.class, IntegrationTestConfig.class},properties = {"spring.cloud.config.import-check.enabled=false", "app_config_name=transactionRetriggerService", "micro.service.password="})
@EnableAutoConfiguration(exclude = {ActiveMQAutoConfiguration.class})
@ActiveProfiles({"test"})
@DirtiesContext
class TransactionRetriggerServiceApplicationTests {
	
	static {
		System.setProperty("app_config_name", "transactionRetriggerService");
	}

	@Autowired
	@Qualifier("integration.cluster.outbound")
	private MessageChannel inChannel;

	@Autowired
	@Qualifier("pollerOutChannel")
	private MessageChannel retriggerInChannel;

	@Autowired
	@Qualifier("errorChannel")
	private MessageChannel errorChannel;

	@Autowired
	@Qualifier("logChannel")
	private PollableChannel logChannel;

	@Autowired
	private RetriggerService service;


	private ObjectMapper mapper;

	private ClientAndServer mockServer;
	

	@MockBean
	ElasticSearchClient esClient;	
	
	@MockBean
	RabbitMQClient rabbitMQClient;

	@MockBean
	RestTemplateBuilder restTemplateBuilder;
	
	@MockBean
	@Qualifier("OAUTH_WEBCLIENT")
	RestTemplate restTemplate;
	
	private CountDownLatch lock = new CountDownLatch(1);

	@BeforeEach
	void setUp() {
		mapper = new ObjectMapper().registerModule(new JodaModule());
		mockServer = startClientAndServer(12345);
	}

	@AfterEach
	void tearDown() {
		mockServer.stop();
	}

	/*
	 * @Test public void testSaveRetriggerPayloadFlow() throws Exception {
	 * GenericTemplate template =
	 * mapper.readValue(getRequestPayload("/data/request.json"),
	 * GenericTemplate.class);
	 * 
	 * Message<?> msg = MessageBuilder.withPayload(template).build();
	 * inChannel.send(msg);
	 * 
	 * Message<?> outMsg = logChannel.receive(10000l); RetriggerPayload payload =
	 * (RetriggerPayload) outMsg.getPayload();
	 * assertNotNull("RetriggerPayload is not null", payload);
	 * assertNotNull("RetriggerPayload Id is not null", payload.getId()); }
	 */

	/*
	 * @Test public void testRetrigger() throws Exception { mockHTTPRequests();
	 * ElasticSearchPayload esPayload = new ElasticSearchPayload(); Query query =
	 * new Query(); Bool bool = new Bool(); query.setBool(bool);
	 * esPayload.setQuery(query); Map<String, Object> esResponse = new
	 * HashMap<String, Object>(); esResponse.put("_count", 0);
	 * when(esClient.getCount(Mockito.anyString(),
	 * Mockito.any())).thenReturn(esResponse); GenericTemplate template =
	 * mapper.readValue(getRequestPayload("/data/request.json"),
	 * GenericTemplate.class);
	 * service.saveRetriggerPayload(service.buildRetriggerPayload(template)); while
	 * (service.getRetriggerPayload().size() == 0) { lock.await(10000,
	 * TimeUnit.MILLISECONDS); }
	 * 
	 * Map<String, Object> esResponse2 = new HashMap<String, Object>(); Map<String,
	 * Object> hits = new HashMap<String, Object>(); List<Map<String, Object>> hits1
	 * = new ArrayList<Map<String, Object>>(); hits.put("hits", hits1);
	 * esResponse2.put("hits", hits);
	 * when(esClient.getPayloadFromES(Mockito.anyString(),
	 * Mockito.any())).thenReturn(esResponse2);
	 * 
	 * Message<?> msg = MessageBuilder.withPayload("trigger").build();
	 * retriggerInChannel.send(msg);
	 * 
	 * Message<?> outMsg = logChannel.receive(10000l); RetriggerPayload payload =
	 * (RetriggerPayload) outMsg.getPayload();
	 * assertNotNull("RetriggerPayload is not null", payload);
	 * assertEquals("RetriggerPayload status is success", "S", payload.getStatus());
	 * }
	 */

	@Test
	void exceptionTest() throws Exception {
		mockHTTPRequests();
		GenericTemplate template = mapper.readValue(getRequestPayload("/data/request.json"), GenericTemplate.class);
		RetriggerPayload payload = service.buildRetriggerPayload(template);
		payload.setDedupKey("12345");
		service.saveRetriggerPayload(payload);
		errorChannel.send(MessageBuilder
				.withPayload(new MessageHandlingException(MessageBuilder.withPayload(payload).build())).build());

		lock.await(2000, TimeUnit.MILLISECONDS);
		//repository.findById(payload.getId());
		assertNotNull(payload, "RetriggerPayload is not null");
		assertEquals("N", payload.getStatus(), "RetriggerPayload status is error");

	}

	private String getRequestPayload(String file) throws IOException {
		return ResourceUtils.streamToString(getClass().getResourceAsStream(file));
	}

	private void mockHTTPRequests() throws IOException {
		mockServer.when(request().withMethod("GET").withPath("/audit/_count"))
				.respond(response().withStatusCode(200)
						.withBody("""
								{
								  "count": 81959785,
								  "_shards": {
								    "total": 5,
								    "successful": 5,
								    "skipped": 0,
								    "failed": 0
								  }
								}\
								"""));

		mockServer
				.when(request().withMethod("DELETE")
						.withPath("/dedup/Test_Process/eda32872-fe65-c0df-678b-50582d483d46/66681374"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody("{\"status\": \"FAILED\",\"message\": \"RECORD DOESNOT EXIST\"}"));

		mockServer.when(request().withMethod("PUT").withPath("/api/lockStatus/unlock"))
				.respond(response().withStatusCode(202));

		mockServer.when(request().withMethod("GET").withPath("/transaction/payload"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/payload.json")));
		mockServer.when(request().withMethod("GET").withPath("/api/transaction/count"))
				.respond(response().withStatusCode(200)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/transactionCount.json")));

		mockServer.when(request().withMethod("GET").withPath("/api/transaction/payload"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/payload_DB.json")));

		mockServer.when(request().withMethod("POST").withPath("/transaction/search"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody(getRequestPayload("/data/transaction.json")));

		mockServer.when(request().withMethod("POST").withPath("/api/exchanges/ACTMON/test.exg/publish"))
				.respond(response().withStatusCode(202)
						.withHeaders(new Header("Content-Type", "application/json; charset=utf-8"))
						.withBody("{\"routed\": true}"));

		mockServer.when(request().withMethod("POST").withPath("/actmon.ws")).respond(response().withStatusCode(202)
				.withHeaders(new Header("Content-Type", "application/json; charset=utf-8")).withBody("ok"));
	}
}
