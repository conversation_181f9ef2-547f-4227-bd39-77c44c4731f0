/*
 * package com.emc.it.eis.transaction.retrigger.config;
 * 
 * import org.springframework.beans.factory.annotation.Value; import
 * org.springframework.context.annotation.Bean; import
 * org.springframework.context.annotation.Configuration;
 * 
 * import feign.Logger; import feign.auth.BasicAuthRequestInterceptor;
 * 
 * @Configuration public class BeanConfiguration {
 * 
 * @Bean BasicAuthRequestInterceptor basicAuthRequestInterceptor(
 * 
 * @Value("${micro.service.username}") String username,
 * 
 * @Value("${micro.service.password}") String password) { return new
 * BasicAuthRequestInterceptor(username, password); }
 * 
 * @Bean Logger.Level feignLoggerLevel() { return Logger.Level.FULL; } }
 */

