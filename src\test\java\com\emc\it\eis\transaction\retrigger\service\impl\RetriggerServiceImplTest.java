package com.emc.it.eis.transaction.retrigger.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.emc.it.eis.alert.service.api.domain.QDetail;
import com.emc.it.eis.alert.service.api.domain.RetriggerParams;
import com.emc.it.eis.transaction.retrigger.client.ElasticSearchClient;
import com.emc.it.eis.transaction.retrigger.client.TransactionServiceClient;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload;
import com.emc.it.eis.transaction.retrigger.domain.Property;
import com.emc.it.eis.transaction.retrigger.domain.RetriggerRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@ExtendWith(MockitoExtension.class)
class RetriggerServiceImplTest {

	@InjectMocks
	protected RetriggerServiceImpl retriggerServiceImpl;

	@Mock
	private TransactionServiceClient payloadClient;

	@Mock
	private ElasticSearchClient elasticSearchClient;

	@Mock
	private ObjectMapper objectMapper;

	@Test
	void constructPayload() {
		ReflectionTestUtils.setField(retriggerServiceImpl, "sourceSearchString", "test");
		try {
			assertNotNull(retriggerServiceImpl.constructPayload("test", "test"));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
	}

	@Test
	void checkFlow() {
		ReflectionTestUtils.setField(retriggerServiceImpl, "sourceSearchString", "test");
		Map<String, Object> properties = new HashMap<>();
		Map<String, Object> hits = new HashMap<>();
		List<Map<String, Object>> hits1 = new ArrayList<>();
		Map<String, String> source = new HashMap<>();

		source.put("partition_id", "test");
		source.put("payload", "test");
		Map<String, Object> sources = new HashMap<>();
		sources.put("_source", source);

		hits1.add(sources);
		hits.put("hits", hits1);
		properties.put("hits", hits);
		assertNotNull(retriggerServiceImpl.checkFlow(properties));
	}

	@Test
	void formatPayload() throws JsonProcessingException {
		ReflectionTestUtils.setField(retriggerServiceImpl, "esKeyDetails", "test");
		Map<String, Object> properties = new HashMap<>();
		Map<String, Object> hits = new HashMap<>();
		List<Map<String, Object>> hits1 = new ArrayList<>();
		Map<String, String> source = new HashMap<>();

		source.put("partition_id", "test");
		source.put("payload", "test");
		source.put("status", "test");
		source.put("app_name", "test");
		source.put("global_transaction_id", "test");
		List<LinkedHashMap> mapList = new ArrayList();
		LinkedHashMap map1 = new LinkedHashMap<>();
		map1.put("name", "name");
		map1.put("value", "value");
		mapList.add(map1);
		source.put("nvp", new ObjectMapper().writeValueAsString(mapList));
		Map<String, Object> sources = new HashMap<>();
		sources.put("_source", source);

		sources.put("_id", "test");

		hits1.add(sources);
		hits.put("hits", hits1);
		properties.put("hits", hits);
		try {
			assertNotNull(retriggerServiceImpl.formatPayload(properties));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Test
	void buildRetriggerPayload() {

		ReflectionTestUtils.setField(retriggerServiceImpl, "esKeyDetails", "test");
		ReflectionTestUtils.setField(retriggerServiceImpl, "sourceSearchString", "test");
		ReflectionTestUtils.setField(retriggerServiceImpl, "index", "test");
		ReflectionTestUtils.setField(retriggerServiceImpl, "esSearchParam", "test");

		Map<String, Object> properties = new HashMap<>();
		Map<String, Object> hits = new HashMap<>();
		List<Map<String, Object>> hits1 = new ArrayList<>();
		Map<String, String> source = new HashMap<>();

		source.put("partition_id", "test");
		source.put("payload", "test");
		source.put("status", "test");
		source.put("app_name", "test");
		source.put("global_transaction_id", "test");
		Map<String, Object> sources = new HashMap<>();
		sources.put("_source", source);
		sources.put("_id", "test");

		hits1.add(sources);
		hits.put("hits", hits1);
		properties.put("hits", hits);

		when(elasticSearchClient.getPropertiesfromELK(Mockito.anyString(), Mockito.anyString(), Mockito.any(ElasticSearchPayload.class))).thenReturn(properties);

		RetriggerParams params = new RetriggerParams();
		params.setDedupKey("test");
		QDetail detail = new QDetail();
		params.setqDetail(detail);
		RetriggerRequest request = new RetriggerRequest();
		request.setRetriggerParams(params);
		try {
			assertNotNull(retriggerServiceImpl.buildRetriggerPayload(request));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Test
	void buildRetriggerPayloadNonKafka() {

		ReflectionTestUtils.setField(retriggerServiceImpl, "esKeyDetails", "test");
		ReflectionTestUtils.setField(retriggerServiceImpl, "sourceSearchString", "test");
		ReflectionTestUtils.setField(retriggerServiceImpl, "index", "test");
		ReflectionTestUtils.setField(retriggerServiceImpl, "esSearchParam", "test");

		Map<String, Object> properties = new HashMap<>();
		Map<String, Object> hits = new HashMap<>();
		List<Map<String, Object>> hits1 = new ArrayList<>();
		Map<String, String> source = new HashMap<>();

		source.put("partition_id", null);
		source.put("payload", "test");
		source.put("status", "test");
		source.put("app_name", "test");
		source.put("global_transaction_id", "test");
		Map<String, Object> sources = new HashMap<>();
		sources.put("_source", source);
		sources.put("_id", "test");

		hits1.add(sources);
		hits.put("hits", hits1);
		properties.put("hits", hits);

		when(elasticSearchClient.getPropertiesfromELK(Mockito.anyString(), Mockito.anyString(), Mockito.any(ElasticSearchPayload.class))).thenReturn(properties);

		RetriggerParams params = new RetriggerParams();
		params.setDedupKey("EMC.globalTransactionId");
		params.setEntityKey("strictorder");
	
		QDetail detail = new QDetail();
		params.setqDetail(detail);
		RetriggerRequest request = new RetriggerRequest();
		request.setRetriggerParams(params);		
		
		List<Property> propertyList = new ArrayList();
		Property prop = new Property();
		prop.setName("EMC.globalTransactionId");
		prop.setValue("EM.globalTransactionId");
		propertyList.add(prop);
		prop = new Property();
		prop.setName("EMC.applicationId");
		prop.setValue("ShipmentConsumerTms");
		propertyList.add(prop);
		prop = new Property();
		prop.setName("strictorder");
		prop.setValue("strictorder");
		propertyList.add(prop);
		when(payloadClient.getPropertiesfromDB(Mockito.any())).thenReturn(propertyList);
		retriggerServiceImpl.getRetriggerPayload("test");
		retriggerServiceImpl.getRetriggerPayload("test", "test");
		retriggerServiceImpl.getRetriggerPayload("test", "test", "test");
		
		try {
			assertNotNull(retriggerServiceImpl.buildRetriggerPayload(request));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
