package com.emc.it.eis.transaction.retrigger.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.emc.it.eis.transaction.retrigger.config.DedupFeignConfiguration;
import com.emc.it.eis.transaction.retrigger.constants.Constants;
import com.emc.it.eis.transaction.retrigger.domain.Response;

@FeignClient(contextId = Constants.DEDUP_SERVICE_NAME   ,  name = Constants.DEDUP_SERVICE_NAME , url = Constants.DEDUP_SERVICE_URL ,configuration= DedupFeignConfiguration.class)
public interface DedupServiceClient {
	@DeleteMapping(Constants.DEDUP_VALUE)
	ResponseEntity<Response> clearDedup(@PathVariable String process,
			@PathVariable String dedupKey, @PathVariable String entityId);
}
