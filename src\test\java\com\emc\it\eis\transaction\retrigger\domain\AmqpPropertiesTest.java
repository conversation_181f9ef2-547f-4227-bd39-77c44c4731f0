package com.emc.it.eis.transaction.retrigger.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class AmqpPropertiesTest {

	@Test
	void data() {
		AmqpProperties obj = new AmqpProperties();

		obj.setAmqpPassword("test");
		assertEquals("test", obj.getAmqpPassword());	

		obj.setAmqpUrl("test");
		assertNotNull(obj.getAmqpUrl());

		obj.setAmqpUserName("test1");
		assertNotNull(obj.getAmqpUserName());

		obj.setAmqpVhost("test");
		assertEquals("test", obj.getAmqpVhost());	

		obj.setLbExchange("test");
		assertNotNull(obj.getLbExchange());

		obj.setLbQConsumers(100);
		assertTrue(obj.getLbQConsumers() == 100);

		obj.setAmqpPassword("test");
		assertNotNull(obj.getAmqpPassword());	

		obj.setLbQueue("test");
		assertNotNull(obj.getLbQueue());

		obj.setParamQConsumers(100);
		assertTrue(obj.getParamQConsumers() == 100);

		obj.setRetriggerParamExchange("test");
		assertNotNull(obj.getRetriggerParamExchange());

		obj.setRetriggerParamQ("test");
		assertNotNull(obj.getRetriggerParamQ());


		obj.setRetriggerParamRoutingKey("test");
		assertNotNull(obj.getRetriggerParamRoutingKey());
	}
}
