package com.emc.it.eis.transaction.retrigger.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;

class NameValuePairTest {

	@Test
	void data() {
		NameValuePair obj = new NameValuePair();

		obj.setName("test");
		assertEquals("test", obj.getName());	

		obj.setValue("test");
		assertNotNull(obj.getValue());

		obj.toString();			
	}
}
