spring.data.elasticsearch.cluster-name=actmon
spring.data.elasticsearch.cluster-nodes=ilabecsdev1.isus.emc.com:9300

transaction.retrigger.retrigger-param-q=AIC.BTM.ALERT.RETRIGGER.Q
transaction.retrigger.retrigger-param-exchange=AIC.BTM.RETRIGGER.CLSTR.EXG
transaction.retrigger.retrigger-param-routing-key=AIC.BTM.ALERT.RETRIGGER.Q

transaction.retrigger.lb-exchange=AIC.BTM.RETRIGGER.PAYLOAD.EXG
transaction.retrigger.lb-routing-key=AIC.BTM.RETRIGGER.PAYLOAD.Q
transaction.retrigger.lb-queue=AIC.BTM.RETRIGGER.PAYLOAD.Q
transaction.retrigger.lb-q-consumers=20

transaction.retrigger.poll.interval=10000

dedup.service.url=http://localhost:12345/dedup
micro.service.username=aicrestuser

strictorder.service.url=http://localhost:12345/api

rabbitmq.service.url=http://localhost:12345

payload.service.url=http://localhost:12345/api

actmon.service.url=http://localhost:12345

feign.hystrix.enabled=false

#Actmon Properties
transaction.retrigger.actmon.environemnt=DEV
transaction.retrigger.actmon.domain=IT
transaction.retrigger.actmon.process=transaction-retrigger-service
transaction.retrigger.actmon.serviceName=transaction-retrigger-service
transaction.retrigger.actmon.serviceVerion=3.0.0-SNAPSHOT
transaction.retrigger.actmon.appName=transactionRetriggerService

spring.jackson.serialization.write-dates-as-timestamps=true

#Cluster Properties
integration.cluster.application.id=transactionRetriggerService
integration.cluster.keepalive.interval=15000
integration.cluster.monitor.interval=30000
integration.cluster.single.source=true
integration.cluster.inbound.adapter=integrationConfig.pollerEndpoint.inboundChannelAdapter

spring.main.allow-bean-definition-overriding=true


elastic.search.url=http://localhost:12345
elastic.service.username=aicrestuser
redis.service.url=http://localhost:12345


####transaction-retrigger-service####
rabbitmq.service.username=aicrestuser

transactionretrigger.security.username=aicrestuser


index=audit_dev
esSearchParam=_search
source.search.string=receiver,alternate_business_identifier,serviceVersion,@version,id,event,summary,appUser,timestamp_id,transactionMode,service_name,eventCode,environment,@timestamp,thread_name,domain,eventSubcode,step,detail,host_name,payload
es.key.details=app_name,date_time,business_identifier,global_transaction_id,status
logging.level.root=info
payloadParams=partition_id,payload,app_name
spring.sql.init.mode=never
#api.security.username=eisrestuser
#spring.profiles.active=test

server.port=8098
APP_HOST=localhost
APP_PORT=8098

spring.jpa.database-platform = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false 

feign.client.config.default.connectTimeout: 160000000
feign.client.config.default.readTimeout: 160000000
management.security.enabled=false

spring.security.oauth2.client.registration.dias-creds.provider=dias
spring.security.oauth2.client.registration.dias-creds.client-id=4acab72b-12fe-480a-aafd-6c3e3b852d2c
spring.security.oauth2.client.registration.dias-creds.client-secret=kBQVwb13
spring.security.oauth2.client.registration.dias-creds.authorization-grant-type=client_credentials


spring.security.oauth2.client.provider.dias.token-uri=https://oauth2-api-dev-dci.ausvdc02.pcf.dell.com/oauth2/api/v3/token


aic.oauth2.enabled.feignclient=true
aic.oauth2.enabled.resttemplate=true
aic.oauth2.enabled.webclient=false