package com.emc.it.eis.transaction.retrigger.integration;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.integration.annotation.Filter;
import org.springframework.integration.annotation.InboundChannelAdapter;
import org.springframework.integration.annotation.Poller;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.annotation.Splitter;
import org.springframework.integration.annotation.Transformer;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.messaging.Message;

import com.emc.it.eis.alert.service.api.domain.GenericTemplate;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;

@Configuration
public class IntegrationConfig {
	@Value("${transaction.retrigger.max.count:3}")
	private int retriggerCount;
	@Autowired
	private RetriggerService service;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(IntegrationConfig.class);

	@Bean
	public DirectChannel transformerOutChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel filterInChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel transformerInChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel loggingInChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel pollerOutChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel activatorInChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel lbQOutChannel() {
		return new DirectChannel();
	}

	@Bean
	public DirectChannel updateRecordInChannel() {
		return new DirectChannel();
	}

	@Filter(inputChannel = "integration.cluster.outbound", outputChannel = "transformerInChannel", discardChannel = "nullChannel")
	public boolean filterMessage(GenericTemplate template) {
		return template.getRetriggerParams() != null ? true : false;
	}

	@Transformer(inputChannel = "transformerInChannel", outputChannel = "filterInChannel")
	public RetriggerPayload getRetriggerPayload(GenericTemplate template) {
		return service.buildRetriggerPayload(template);
	}

	@Filter(inputChannel = "filterInChannel", outputChannel = "nullChannel", discardChannel = "transformerOutChannel")
	public boolean filterRetriggerMessage(RetriggerPayload payload) {
		if (payload.getGlobalTransactionId() == null) {
			return false;
		} else {
			return service
					.getRetriggerPayload(payload.getGlobalTransactionId(),
							payload.getRetriggerApp()).size() > retriggerCount
					&& service.getRetriggerPayload(
					payload.getBusinessIdentifer(),
					payload.getRetriggerApp(), "N").isEmpty() ? true
					: false;
		}

	}

	@ServiceActivator(inputChannel = "transformerOutChannel", outputChannel = "loggingInChannel")
	public RetriggerPayload saveRetriggerPayload(RetriggerPayload payload) {
	//	LOGGER.debug("retrigger date whikle saving: "+payload.getRetriggerDate());
		//	LOGGER.debug("retrigger date after saving: "+savedPayload.getRetriggerDate());
		return service.saveRetriggerPayload(payload);
	}

	@InboundChannelAdapter(value = "pollerOutChannel", poller = @Poller(fixedDelay = "${transaction.retrigger.poll.interval}"), autoStartup = "false")
	public Date pollerEndpoint() {
		return new Date();
	}

	@Splitter(inputChannel = "pollerOutChannel", outputChannel = "updateRecordInChannel")
	public List<RetriggerPayload> getRetriggerPayload(Message<?> msg) {
	//	LOGGER.debug("in splitter method " );
		//	LOGGER.debug("in retriggerPayloadList size " + retriggerPayloadList.size());
	//	LOGGER.debug("in retriggerPayloadList first object " + retriggerPayloadList.get(0));
		return service.getRetriggerPayload();
	}

	@ServiceActivator(inputChannel = "updateRecordInChannel", outputChannel = "lbQOutChannel")
	public RetriggerPayload updateRetriggerPayload(RetriggerPayload payload) {
	//	LOGGER.debug("in updateRetriggerPayload method " );
		return service.updateRetriggerPayloadStatus(payload, "P");
	}

	@Bean
	@Profile(value = {"default", "cloud", "local"})
	@ServiceActivator(inputChannel = "loggingInChannel")
	public LoggingHandler logging() {
		LoggingHandler adapter = new LoggingHandler(LoggingHandler.Level.INFO);
		adapter.setLoggerName("RetriggerPayload_Logger");
		return adapter;
	}
}
