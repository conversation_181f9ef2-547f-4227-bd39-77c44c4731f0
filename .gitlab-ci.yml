variables:
  Actmon_logging_clientrmq: 'no'
  Package_Type: war
  ContivoSupport: 'no'
  Gemfire_connectivity: 'yes'
  SONAR_EXCLUSIONS: '**/AmqpConfig.java,**/TransactionRetriggerServiceApplication.java,**/controller/HomeController.java,**/ServletInitializer.java,**/SecurityConfig.java'
  DEV_PCF_ORG: AICCoreDevOrg
  DEV_PCF_SPACE: cnieap-cit-dev
  PCF_MF_FILE_DEV: manifest-gsms-dev-dtc.yml
  PCF_MF_FILE_DES: manifest_des_dtc.yml
  PCF_MF_FILE_QAS: manifest-qas-dtc.yml
  PCF_MF_FILE_QAX: manifest_qax_dtc.yml
  PCF_MF_FILE_QAV: manifest_qav_dtc.yml
  PCF_MF_FILE_PERF: manifest_prf_dtc.yml
  PCF_MF_FILE_PROD: manifest-prod-dtc.yml
  PCF_MF_FILE_DR: manifest-prod-dtc.yml
  manifest_appid: 41918
  manifest_appid_prod: 44397
  DEV_KOB_SPACE: aicshd-cit-dev
  NAMESPACE: aicshd-cit-dev
  HELM_NAMESPACE: aicshd-cit-dev
  HARBOR_REPO_NAMESPACE: "ipaas/realtime"
  VAULT_CONFIG_PATH_DEV: "kv/KOB/AT1-NP"
  KOB_VALUES_FILE_DEV: values-dev-dtc.yml
  KOB_VALUES_FILE_DES: values_des_dtc.yml
  KOB_VALUES_FILE_QAS: values-qas-dtc.yml
  KOB_VALUES_FILE_QAX: values_qax_dtc.yml
  KOB_VALUES_FILE_QAV: values_qav_dtc.yml
  KOB_VALUES_FILE_PERF: values_prf_dtc.yml
  KOB_VALUES_FILE_PROD: values-prod-dtc.yml
  KOB_VALUES_FILE_DR: values-prod-dtc.yml
  CHART_NAME: transaction-retrigger-service
include:
- project: ServiceAICNP/shared-template
  ref: v3.0-aic-kob
  file: aic/aic-jobs-cloudnative-kob.yml
