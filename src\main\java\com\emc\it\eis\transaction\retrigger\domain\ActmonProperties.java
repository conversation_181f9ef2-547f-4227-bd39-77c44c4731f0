package com.emc.it.eis.transaction.retrigger.domain;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
@Component
@ConfigurationProperties("transaction.retrigger.actmon")
public class ActmonProperties {
	private String environemnt;
	private String domain;
	private String process;
	private String serviceName;
	private String serviceVerion;
	private String appName;

	/**
	 * @return the environemnt
	 */
	public String getEnvironemnt() {
		return environemnt;
	}

	/**
	 * @return the domain
	 */
	public String getDomain() {
		return domain;
	}

	/**
	 * @return the process
	 */
	public String getProcess() {
		return process;
	}

	/**
	 * @return the serviceName
	 */
	public String getServiceName() {
		return serviceName;
	}

	/**
	 * @return the serviceVerion
	 */
	public String getServiceVerion() {
		return serviceVerion;
	}

	/**
	 * @return the appName
	 */
	public String getAppName() {
		return appName;
	}

	/**
	 * @param environemnt
	 *            the environemnt to set
	 */
	public void setEnvironemnt(String environemnt) {
		this.environemnt = environemnt;
	}

	/**
	 * @param domain
	 *            the domain to set
	 */
	public void setDomain(String domain) {
		this.domain = domain;
	}

	/**
	 * @param process
	 *            the process to set
	 */
	public void setProcess(String process) {
		this.process = process;
	}

	/**
	 * @param serviceName
	 *            the serviceName to set
	 */
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	/**
	 * @param serviceVerion
	 *            the serviceVerion to set
	 */
	public void setServiceVerion(String serviceVerion) {
		this.serviceVerion = serviceVerion;
	}

	/**
	 * @param appName
	 *            the appName to set
	 */
	public void setAppName(String appName) {
		this.appName = appName;
	}

}
