package com.emc.it.eis.transaction.retrigger.domain;

import java.util.List;

public class Bool {

private List<Must> must;

private List<Should> should;

private List<ESFilter> filter;

public List<Must> getMust() {
return must;
}

public void setMust(List<Must> must) {
this.must = must;
}

public List<Should> getShould() {
	return should;
}

public void setShould(List<Should> should) {
	this.should = should;
}

public List<ESFilter> getFilter() {
return filter;
}

public void setFilter(List<ESFilter> filter) {
this.filter = filter;
}

}
