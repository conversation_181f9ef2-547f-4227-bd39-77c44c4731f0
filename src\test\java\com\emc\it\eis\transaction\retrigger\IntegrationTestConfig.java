package com.emc.it.eis.transaction.retrigger;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.integration.annotation.BridgeFrom;
import org.springframework.integration.annotation.BridgeTo;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.channel.QueueChannel;

@Configuration
@Profile("test")
public class IntegrationTestConfig {
	@Bean
	@BridgeFrom(value = "loggingInChannel")
	public QueueChannel logChannel() {
		return new QueueChannel();
	}

	@Bean
	@BridgeTo("transactionfilterChannel")
	public DirectChannel lbQOutChannel() {
		return new DirectChannel();
	}
	
}
