package com.emc.it.eis.transaction.retrigger.service;

import java.io.IOException;
import java.util.List;

import com.emc.it.eis.alert.service.api.domain.GenericTemplate;
import com.emc.it.eis.transaction.retrigger.domain.RetriggerRequest;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

public interface RetriggerService {
	public RetriggerPayload buildRetriggerPayload(GenericTemplate template);	
	
	public RetriggerPayload buildRetriggerPayload(RetriggerRequest request) throws Json<PERSON>arseException, <PERSON>son<PERSON>appingException, IOException;
	
	public RetriggerPayload saveRetriggerPayload(RetriggerPayload payload);
	
	public List<RetriggerPayload> getRetriggerPayload();
	
	public List<RetriggerPayload> getRetriggerPayload(String appName);
	
	public List<RetriggerPayload> getRetriggerPayload(String gid, String appName);
	
	public List<RetriggerPayload> getRetriggerPayload(String bid, String appName, String status);
	
	public RetriggerPayload updateRetriggerPayloadStatus(RetriggerPayload payload, String status);

}
