package com.emc.it.eis.transaction.retrigger.client;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.emc.it.eis.transaction.retrigger.config.RabitFeignConfiguration;
import com.emc.it.eis.transaction.retrigger.constants.Constants;
import com.emc.it.eis.transaction.retrigger.domain.ResubmitRequest;

@FeignClient(contextId = Constants.RABBITMQ_SERVICE_NAME , name = Constants.RABBITMQ_SERVICE_NAME, url = Constants.RABBITMQ_SERVICE_URL,configuration = RabitFeignConfiguration.class)
public interface RabbitMQClient {
	@PostMapping("/api/exchanges/{vhost}/{exchange}/publish")
	Map<String, Boolean> publishPayload(@PathVariable String vhost,
			@PathVariable String exchange,
			@RequestBody ResubmitRequest request);
}
