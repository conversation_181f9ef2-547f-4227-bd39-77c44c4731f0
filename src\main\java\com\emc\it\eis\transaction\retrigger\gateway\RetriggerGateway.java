package com.emc.it.eis.transaction.retrigger.gateway;

import java.util.concurrent.Future;

import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;

import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;

@MessagingGateway(name="retriggerGateway")
public interface RetriggerGateway {
	@Gateway(requestChannel = "dedupDeleteIn")
	public Future submitRetriggerRequest(RetriggerPayload payload);
}
