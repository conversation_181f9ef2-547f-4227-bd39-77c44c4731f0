package com.emc.it.eis.transaction.retrigger.integration;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.emc.it.eis.alert.service.api.domain.GenericTemplate;
import com.emc.it.eis.alert.service.api.domain.RetriggerParams;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;

@ExtendWith(MockitoExtension.class)
class IntegrationConfigTest {

	@InjectMocks
	protected IntegrationConfig integrationConfig;

	@Mock
	private RetriggerService service;

	@Test
	void filterMessage() {
		GenericTemplate template = new GenericTemplate();
		template.setRetriggerParams(new RetriggerParams());
		assertTrue(integrationConfig.filterMessage(template));

		RetriggerPayload payload = new RetriggerPayload();
		when(service.buildRetriggerPayload(Mockito.any(GenericTemplate.class))).thenReturn(payload);

		integrationConfig.getRetriggerPayload(template);
	}

	@Test
	void clearDedup() {
		integrationConfig.logging();
	}
}
