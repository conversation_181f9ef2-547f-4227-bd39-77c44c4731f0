package com.emc.it.eis.transaction.retrigger.integration;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import com.emc.it.eis.transaction.retrigger.client.ActmonClient;
import com.emc.it.eis.transaction.retrigger.client.DedupServiceClient;
import com.emc.it.eis.transaction.retrigger.client.ElasticSearchClient;
import com.emc.it.eis.transaction.retrigger.client.RabbitMQClient;
import com.emc.it.eis.transaction.retrigger.client.RedisClient;
import com.emc.it.eis.transaction.retrigger.client.StrictOrderClient;
import com.emc.it.eis.transaction.retrigger.client.TransactionServiceClient;
import com.emc.it.eis.transaction.retrigger.domain.ElasticSearchPayload2;
import com.emc.it.eis.transaction.retrigger.domain.LockStatus;
import com.emc.it.eis.transaction.retrigger.domain.Response;
import com.emc.it.eis.transaction.retrigger.entity.RetriggerPayload;
import com.emc.it.eis.transaction.retrigger.service.ActmonService;
import com.emc.it.eis.transaction.retrigger.service.RetriggerService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@ExtendWith(MockitoExtension.class)
class RetriggerConfigTest {

	@InjectMocks
	protected RetriggerConfig retriggerConfig;

	@Mock
	private RetriggerService service;

	@Mock
	private DedupServiceClient dedupClient;

	@Mock
	private StrictOrderClient strictOrderClient;

	@Mock
	private TransactionServiceClient payloadClient;

	@Mock
	private RabbitMQClient rabbitMQClient;

	@Mock
	private ActmonClient actmonClient;

	@Mock
	private ActmonService actmonService;

	@Mock
	private RedisClient redisClient;

	@Mock
	private ElasticSearchClient esClient;

	@Mock
	private ObjectMapper objectMapper;

	@Test
	void constructCountPayload() {

		RetriggerPayload payload = new RetriggerPayload();
		payload.setTransactionDate(new Date());
		assertNotNull(retriggerConfig.constructCountPayload(payload));
		payload.setBusinessIdentifer("test1");
		assertTrue(retriggerConfig.checkForDuplicateTransaction(payload));
	}

	@Test
	void clearDedup() {

		RetriggerPayload payload = new RetriggerPayload();
		payload.setTransactionDate(new Date());
		Response response = new Response();
		ResponseEntity<Response> responseEntity = new ResponseEntity(response, HttpStatus.OK);

		try {
			assertNotNull(retriggerConfig.clearDedup(payload));
		} catch (JsonProcessingException e) {			
			e.printStackTrace();
		}

		payload.setDedupProcess("test");
		payload.setDedupKey("test");
		payload.setStrictOrderKey("test");
		when(dedupClient.clearDedup(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(responseEntity);
		try {
			assertNotNull(retriggerConfig.clearDedup(payload));
		} catch (JsonProcessingException e) {			
			e.printStackTrace();
		}		
	}

	@Test
	void unlockStrictOrder() {

		RetriggerPayload payload = new RetriggerPayload();
		payload.setTransactionDate(new Date());
		payload.setBusinessIdentifer("test1");
		payload.setDedupProcess("test");
		payload.setDedupKey("test");
		payload.setStrictOrderKey("test");
		payload.setStrictOrderProcess("test");

		String result = "test";
		when(strictOrderClient.unlockRecord(Mockito.any(LockStatus.class))).thenReturn(result);
		try {
			assertNotNull(retriggerConfig.unlockStrictOrder(payload));
		} catch (JsonProcessingException e) {			
			e.printStackTrace();
		}		
	}

	@Test
	void buildESQueryPayload() {
		ReflectionTestUtils.setField(retriggerConfig, "payloadParams", "test");

		RetriggerPayload payload = new RetriggerPayload();
		payload.setTransactionDate(new Date());

		payload.setBusinessIdentifer("test1");
		try {
			assertNotNull(retriggerConfig.buildESQueryPayload(payload));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

	}

	@Test
	void getPayloadfromDB() {

		ReflectionTestUtils.setField(retriggerConfig, "index", "test");
		ReflectionTestUtils.setField(retriggerConfig, "payloadParams", "test");

		RetriggerPayload payload = new RetriggerPayload();
		payload.setTransactionDate(new Date());
		payload.setBusinessIdentifer("test1");
		payload.setDedupProcess("test");
		payload.setDedupKey("test");
		payload.setStrictOrderKey("test");
		payload.setStrictOrderProcess("test");

		Map<String, Object> payloadMap = new HashMap<>();
		Map<String, Object> hits = new HashMap<>();
		//(Map<String, Object>) payloadMap.get("hits");
		List<Map<String, Object>> hits1 = new ArrayList<>();
		Map<String, String> source = new HashMap<>();

		source.put("partition_id", "test");
		source.put("payload", "test");
		Map<String, Object> sources = new HashMap<>();
		sources.put("_source", source);

		hits1.add(sources);
		hits.put("hits", hits1);
		payloadMap.put("hits", hits);

		when(esClient.getPayloadFromES(Mockito.anyString(), Mockito.any(ElasticSearchPayload2.class))).thenReturn(payloadMap);
		try {
			assertNotNull(retriggerConfig.getPayloadfromDB(payload));
		} catch (JsonProcessingException e) {			
			e.printStackTrace();
		}		
	}
}
