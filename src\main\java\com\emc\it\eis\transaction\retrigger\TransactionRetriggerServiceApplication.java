package com.emc.it.eis.transaction.retrigger;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProvider;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProviderBuilder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;

import com.emc.it.eis.activity.mapper.CustomMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

@EnableFeignClients
@SpringBootApplication(exclude = {RabbitAutoConfiguration.class})
@ImportResource(locations = {"classpath:/META-INF/spring/integration/cluster/cluster-control-context.xml",
      "classpath:/spring-confing/bean-config.xml"})
@ComponentScan(basePackages = {"com.emc.it.eis.transaction.retrigger", "com.emc.it.eis.oauth2client", "com.emc.it.eis.oauth2server"})
public class TransactionRetriggerServiceApplication
{

   public static void main(String[] args)
   {
      SpringApplication.run(TransactionRetriggerServiceApplication.class, args);
   }

   @Bean
   ObjectMapper objectMapper()
   {
      ObjectMapper mapper = new CustomMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      return mapper;
   }

   @Bean
   OAuth2AuthorizedClientManager authorizedClientManager(
         ClientRegistrationRepository clientRegistrationRepository,
         OAuth2AuthorizedClientService clientService)
   {

      OAuth2AuthorizedClientProvider authorizedClientProvider =
            OAuth2AuthorizedClientProviderBuilder.builder()
                  .clientCredentials()
                  .build();

      AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager =
            new AuthorizedClientServiceOAuth2AuthorizedClientManager(
                  clientRegistrationRepository, clientService);
      authorizedClientManager.setAuthorizedClientProvider(authorizedClientProvider);

      return authorizedClientManager;
   }

}
