[{"id": 168953007, "payload": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><v1:CustomerAutoCreateRequest xmlns:v1=\"http://emc.com/it/enterprise/contract/CustomerImapService/v1\">\n         <v11:PayloadContext xmlns:v11=\"http://emc.com/it/enterprise/data/v1\">\n            <v12:ApplicationProfile xmlns:v12=\"http://emc.com/it/enterprise/msg/v1\">\n               <v12:AppName>SFDC</v12:AppName>\n            </v12:ApplicationProfile>\n         </v11:PayloadContext>\n         <v11:Document xmlns:v11=\"http://emc.com/it/enterprise/data/v1\">\n            <v11:QuoteNumber/>\n            <v11:SourceObject>leadheader</v11:SourceObject>\n            <v11:SourceField>ID</v11:SourceField>\n            <v11:SourceValue>00Qn0000002CTfSEAW</v11:SourceValue>\n            <v11:SalesOrganization/>\n            <v11:Address>\n               <v11:PartyName>Sandy Embargo ACCust4</v11:PartyName>\n               <v11:Addressline1>2104 Windsor Ridge</v11:Addressline1>\n               <v11:City>Westborough</v11:City>\n               <v11:State/>\n               <v11:Region/>\n               <v11:PostalCode/>\n               <v11:CountryCode>CU</v11:CountryCode>\n            </v11:Address>\n         </v11:Document>\n      </v1:CustomerAutoCreateRequest>", "transaction": {"id": 382541825, "appName": "customer-webservice-imap", "globalTransactionId": "00000158-4440-3c47-0000-015844403c47", "status": "STARTED"}}, {"id": 168953008, "payload": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<tns:SearchHubRequest xmlns:tns=\"http://emc.com/it/enterprise/contract/CustomerImapService/v1\" xmlns:sch1=\"http://emc.com/it/enterprise/msg/v1\" xmlns:sch2=\"http://emc.com/it/enterprise/data/v1\">\n<sch2:PayloadContext>\n<sch1:ApplicationProfile>\n<sch1:AppName>SFDC</sch1:AppName>\n</sch1:ApplicationProfile>\n</sch2:PayloadContext>\n<sch2:Document>\n<sch2:Address>\n<sch2:PartyName>Sandy Embargo ACCust4</sch2:PartyName>\n<sch2:Addressline1>2104 Windsor Ridge</sch2:Addressline1>\n<sch2:City>Westborough</sch2:City>\n<sch2:CountryCode>CU</sch2:CountryCode>\n</sch2:Address>\n</sch2:Document>\n</tns:SearchHubRequest>\n", "transaction": {"id": 382541826, "appName": "customer-webservice-imap", "globalTransactionId": "00000158-4440-3c47-0000-015844403c47", "status": "STARTED"}}, {"id": 168953010, "payload": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<svc:DuplicateCheckRequest xmlns:svc=\"http://emc.com/it/enterprise/contract/CustomerImapService/v1\" xmlns:msg=\"http://emc.com/it/enterprise/msg/v1\" xmlns:data=\"http://emc.com/it/enterprise/data/v1\">\n<data:PayloadContext>\n<msg:ApplicationProfile>\n<msg:AppName>SFDC</msg:AppName>\n</msg:ApplicationProfile>\n</data:PayloadContext>\n<data:Document>\n<data:Address>\n<data:PartyName>SANDY EMBARGO ACCUST4</data:PartyName>\n<data:SingleAddressline>2104 Windsor Ridge;Westborough;CU</data:SingleAddressline>\n<data:Addressline1>2104 Windsor Ridge</data:Addressline1>\n<data:SrcAddressline1>2104 Windsor Ridge</data:SrcAddressline1>\n<data:City>Westborough</data:City>\n<data:Region>CU-</data:Region>\n<data:PostalCode>99999</data:PostalCode>\n<data:CountryCode>CU</data:CountryCode>\n</data:Address>\n</data:Document>\n</svc:DuplicateCheckRequest>\n", "transaction": {"id": 382541829, "appName": "customer-webservice-imap", "globalTransactionId": "00000158-4440-3c47-0000-015844403c47", "status": "STARTED"}}]