{"took": 972, "timed_out": false, "_shards": {"total": 4, "successful": 4, "skipped": 0, "failed": 0}, "hits": {"total": {"value": 4, "relation": "eq"}, "max_score": 14.0669365, "hits": [{"_index": "audit_qax", "_type": "_doc", "_id": "*************", "_score": 14.0669365, "_routing": "email-redis", "_source": {"partition_id": "2", "global_transaction_id": "QAXalerttesting123", "app_name": "email-redis", "logstash_process": "actmon_shared_kafka_qax", "date_time": "2020-11-04T08:18:00.678Z", "offset_id": "*********", "business_identifier": "QAXalerttesting123", "guid": "45eb6d54e1c7429c9f0f50e4e21577ed", "inventory_join": {"parent": "email-redis", "name": "shared"}, "isPayloadTempered": null, "status": "STARTED"}}, {"_index": "audit_qax", "_type": "_doc", "_id": "*************", "_score": 14.0669365, "_routing": "email-redis", "_source": {"partition_id": "8", "global_transaction_id": "QAXalerttesting123", "app_name": "email-redis", "logstash_process": "actmon_shared_kafka_qax", "date_time": "2020-11-04T08:20:39.103Z", "offset_id": "********", "business_identifier": "QAXalerttesting123", "guid": "fa59985123154fabbf139e9c4fee5a29", "inventory_join": {"parent": "email-redis", "name": "shared"}, "isPayloadTempered": null, "status": "FAILED"}}, {"_index": "audit_qax", "_type": "_doc", "_id": "*************", "_score": 14.0669365, "_routing": "email-redis", "_source": {"partition_id": "5", "global_transaction_id": "QAXalerttesting123", "app_name": "email-redis", "logstash_process": "actmon_shared_kafka_qax", "date_time": "2020-11-09T06:57:58.946Z", "offset_id": "********", "business_identifier": "QAXalerttesting123", "nvp": [{"name": "EMC.businessIdentifier", "value": "QAXalerttesting123"}, {"name": "EMC.globalTransactionId", "value": "QAXalerttesting123"}, {"name": "EMC.applicationId", "value": "email-redis"}, {"name": "timestamp", "value": "*************"}, {"name": "id", "value": "QAXalerttesting123"}, {"name": "PayloadContext.MessageProfile.Process", "value": ""}], "guid": "9549408b18d347a3b65db9081f84ee8f", "inventory_join": {"parent": "email-redis", "name": "shared"}, "isPayloadTempered": null, "status": "FAILED"}}, {"_index": "audit_qax", "_type": "_doc", "_id": "*************", "_score": 14.0669365, "_routing": "email-redis", "_source": {"partition_id": "10", "global_transaction_id": "QAXalerttesting123", "app_name": "email-redis", "logstash_process": "actmon_shared_kafka_qax", "date_time": "2020-11-09T07:12:40.323Z", "offset_id": "********", "business_identifier": "QAXalerttesting123", "nvp": [{"name": "EMC.businessIdentifier", "value": "QAXalerttesting123"}, {"name": "EMC.globalTransactionId", "value": "QAXalerttesting123"}, {"name": "EMC.applicationId", "value": "email-redis"}, {"name": "timestamp", "value": "*************"}, {"name": "id", "value": "QAXalerttesting123"}, {"name": "PayloadContext.MessageProfile.Process", "value": ""}], "guid": "be2ccc7f18be4481b8c78c0754015c3e", "inventory_join": {"parent": "email-redis", "name": "shared"}, "isPayloadTempered": null, "status": "FAILED"}}]}}