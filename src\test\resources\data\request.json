{"event": {"payloadContext": {"messageProfile": {"domain": "ERP", "process": "test-interface", "serviceName": "test-interface", "serviceVersion": "2.1-SNAPSHOT"}, "applicationProfile": {"appName": "test-interface", "appUser": "CIC"}, "transactionProfile": {"transactionDateTime": {"value": 1331191923000, "timeZoneCode": null, "daylightSavingTimeIndicator": null}, "globalTransactionID": "12052c77-8ec1-47cc-9665-8357930136e0\n\t\t\t", "repostFlag": null, "transactionMode": null, "environment": "<PERSON>", "event": null}, "userArea": null}, "document": {"eventActivity": {"event": "PROCESS", "eventCode": null, "eventSubCode": null, "step": {"value": "inputChannel", "languageID": null}, "status": {"value": "FAILED", "languageID": null}, "summary": null, "detail": {"value": "org.springframework.integration.MessageHandlingException: HTTP request execution failed for URI [http://echsif.isus.emc.com:80/cmx/services/EmcpartyService]\n\t\t\t\t\tat org.springframework.integration.http.outbound.HttpRequestExecutingMessageHandler.handleRequestMessage(HttpRequestExecutingMessageHandler.java:377)\n\t\t\t\t\tat org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:134)\n\t\t\t\t\tat org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:73)\n\t\t\t\t\tat sun.reflect.GeneratedMethodAccessor504.invoke(Unknown Source)\n\t\t\t\t\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:25)\n\t\t\t\t\tat java.lang.reflect.Method.invoke(Method.java:597)\n\t\t\t\t\t", "languageID": null}, "payload": null, "nameValuePairs": {"nameValuePairs": [{"name": "entityKey", "value": "66681374"}, {"name": "EMC.applicationId", "value": "test-interface"}, {"name": "EMC.globalTransactionId", "value": "eda32872-fe65-c0df-678b-50582d483d46"}, {"name": "EMC.payloadType", "value": "{http://emc.com/it/enterprise/contract/distributeGeneralLedger/v1}DistributeGeneralLedgerRequest\n\t\t\t\t\t"}, {"name": "LG_Header", "value": "Interaction=rR5OHO+vsY8NujHqNQEjeP4K;Locus=B+FXq0XneJZOQ5K0ER4p5Q==;Flow=CQAokJFujA9wmMDwNQEjeP4K;Chain=AACewiGYBibVnDjxNQEjeP4K;UpstreamOpID=T8CuyumRytOkLZpU//MllA==;CallerAddress=intmsgdev01;CalleeAddress=intintdev06.isus.emc.com;\n\t\t\t\t\t"}, {"name": "jms_messageId", "value": "ID:ffffffffab7e38aa:4073607b0001:135F1389CA6"}, {"name": "timestamp", "value": "1331191922994"}, {"name": "id", "value": "4bfffc7f-e751-4b0c-9a3f-ec736ddb4a7f"}, {"name": "EMC.SAPNotificationId", "value": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n\t\t\t\t\t\t<Datasource\n\t\t\t\t\t\txmlns=\"com.ibi.table.TableListenerAdapter.CIC_NOTIFICATION.null\">\n\t\t\t\t\t\t<row>\n\t\t\t\t\t\t<NTFID>1</NTFID>\n\t\t\t\t\t\t<SAPID>0000210101</SAPID>\n\t\t\t\t\t\t<TYPE>GeneralLedger</TYPE>\n\t\t\t\t\t\t<STATUS>NEW</STATUS>\n\t\t\t\t\t\t<POLL_ID/>\n\t\t\t\t\t\t</row>\n\t\t\t\t\t\t</Datasource>"}, {"name": "JMSXDeliveryCount", "value": "5"}, {"name": "jms_redelivered", "value": "true"}, {"name": "PayloadContext.MessageProfile.Process", "value": "test-interface"}, {"name": "EMC.processId", "value": "salesorder-oracle11i"}, {"name": "EMC.businessIdentifier", "value": "0000210101"}]}, "businessIdentifier": "0000210101", "alternateBusinessIdentifier": null, "hostName": "intintdev06", "threadID": "glTopicContainer-13262"}}}, "emailParams": {"fromAddress": "<EMAIL>", "toAddress": "<EMAIL>", "subject": "test subject", "template": "email_template", "avoidDuplicate": true, "ttl": 3600000}, "ticketParams": {"configurationItem": "CIC_INTEGRATION", "priority": "3", "shortDescription": "business error", "avoidDuplicate": true, "ttl": 3600000, "duplicateKey": "bid|abid"}, "retriggerParams": {"dedupKey": "EMC.globalTransactionId", "entityKey": "entityKey", "source": "case-producer-servicesfdc", "interval": 20000, "dedupProcess": "Test_Process", "strictOrderProcess": "strictSalesMobilitySROra11iSFDC", "qDetail": {"vHost": "ACTMON", "exchange": "test.exg", "routingKey": "test.queue"}}, "rule": {"ruleName": "Rule 1", "ruleExpression": ".*MessageHandlingException.*", "regularExpression": true, "businessError": true}}