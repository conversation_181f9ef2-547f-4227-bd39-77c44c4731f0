package com.emc.it.eis.transaction.retrigger.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;

class ResubmitRequestTest {

	@Test
	void data() {
		ResubmitRequest obj = new ResubmitRequest();
		
		obj.setPayload("test");
		assertEquals("test", obj.getPayload());	
		
		obj.setProperties("test", "test");
		assertNotNull(obj.getProperties());
		
		obj.setRouting_key("test1");
		assertNotNull(obj.getRouting_key());		
	}
}
