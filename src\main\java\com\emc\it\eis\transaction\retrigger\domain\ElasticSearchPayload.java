package com.emc.it.eis.transaction.retrigger.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

public class ElasticSearchPayload {

private Query query;

@JsonProperty("_source")
@SerializedName("_source")
private Source source;

public Query getQuery() {
return query;
}

public void setQuery(Query query) {
this.query = query;
}

public Source getSource() {
return source;
}

public void setSource(Source source) {
this.source = source;
}


}
